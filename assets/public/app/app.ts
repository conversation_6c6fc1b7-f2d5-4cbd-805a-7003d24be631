console.log('app.ts loaded!!');

import './app.scss';

import { library, dom, icon } from '@fortawesome/fontawesome-svg-core';
import { fas } from '@fortawesome/pro-solid-svg-icons';
import { fal } from '@fortawesome/pro-light-svg-icons';
import { far } from '@fortawesome/pro-regular-svg-icons';
import { fab } from '@fortawesome/free-brands-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

import Vue from 'vue';
import Axios from 'axios';
import * as qs from 'qs';

const facAbusChat = icon({
  // @ts-ignore
  prefix: 'fac',
  // @ts-ignore
  iconName: 'abus-chat',
  // @ts-ignore
  icon: [
    40,
    40,
    [],
    'e001',
    'M 6.1777344,0 C 2.7911448,0 0,2.7911445 0,6.1777344 V 37.648438 a 2.2502249,2.2502249 0 0 0 3.8378906,1.59375 l 7.2128904,-7.212891 h 22.669922 c 3.38659,0 6.177735,-2.791145 6.177735,-6.177735 a 2.2502249,2.2502249 0 0 0 -0.0078,-0.228515 V 6.1777344 C 39.890803,2.7911445 37.09948,0 33.712891,0 Z m 0,4.5039062 H 33.712891 c 0.95341,0 1.675781,0.7204178 1.675781,1.6738282 V 25.851562 a 2.2502249,2.2502249 0 0 0 0,0.01367 c -0.01262,0.941023 -0.72294,1.66211 -1.667969,1.66211 H 10.121094 A 2.2502249,2.2502249 0 0 0 8.5253906,28.191406 L 4.5039062,32.214844 V 6.1777344 c 0,-0.9534104 0.7204182,-1.6738282 1.6738282,-1.6738282 z M 20.419922,8.5703125 c -0.169999,0 -0.310626,0.088988 -0.390625,0.2089844 0,0 -6.110078,9.0511721 -8.580078,12.7011721 h -0.01953 l -1.410157,2.089843 h 0.720703 5.640625 c 0.159999,0 0.28914,-0.100468 0.369141,-0.230468 l 1.460938,-2.470703 c 0.06,-0.12 0.188123,-0.199219 0.328124,-0.199219 h 4.810547 c 0.210002,0 0.38086,0.170858 0.38086,0.380859 v 2.148438 c 0,0.209998 0.168905,0.380859 0.378906,0.380859 H 29 c 0.210002,-0.01002 0.380859,-0.180625 0.380859,-0.390625 V 8.9492188 C 29.380859,8.7392169 29.210001,8.5703125 29,8.5703125 Z m 2.939453,4.0292965 c 0.209998,0 0.371094,0.171093 0.371094,0.371094 v 4.189453 h 0.0098 c 0,0.209998 -0.170858,0.378906 -0.380859,0.378906 h -2.490234 c -0.209998,0 -0.369141,-0.149376 -0.369141,-0.359374 0,-0.09 0.04008,-0.170234 0.08008,-0.240235 l 2.458984,-4.150391 c 0.06,-0.109999 0.180312,-0.189453 0.320313,-0.189453 z',
  ],
});

// Add icons to the library so you can use it in your page
library.add(fas, fal, far, fab, facAbusChat);

// Kicks off the process of finding <i> tags and replacing with <svg>
dom.watch();

//Polyfills
require('../polyfills/polyfills');

// Service Worker nicht in der Entwicklungsumgebung
if ('serviceWorker' in navigator && window.location.href.indexOf('.development.') === -1) {
  window.addEventListener('load', () => {
    navigator.serviceWorker
      .register('/dist/service-worker.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

if (document.getElementById('cookieInfo') !== null) {
  console.log('CookieInfo');
  const vueCookie = new Vue({
    el: '#cookieInfo',
    data: () => {
      return {
        cookieAccepted: <boolean>false,
        cookieRoute: <string>'',
      };
    },
    methods: {
      acceptCookie(): void {
        this.cookieAccepted = true;
        Axios.post(
          this.cookieRoute,
          qs.stringify({
            cookieAccepted: true,
          }),
          { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } },
        )
          .then((response: any) => {
            vueCookie.$destroy();
          })
          .catch((error: any) => {});
      },
    },
  });
}
