<template>
  <div class="form-group row">
    <label class="col-sm-4 col-form-label" for="password" v-html="$t('service/mailGenerator/password')"></label>
    <div class="col-sm-8">
      <div class="row w-full">
        <div class="col-sm-7">
          <input
            v-model="password"
            class="form-control"
            type="text"
            :placeholder="$t('service/mailGenerator/password')" />
          <input id="password" name="password" type="hidden" :value="password" />
        </div>
        <div class="col-sm-5">
          <button class="btn btn-primary" type="button" :disabled="loading" @click.prevent="getPassword">
            <span v-if="loading" class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span>
            {{ loading ? 'loading...' : $t('service/mailGenerator/generate_Password') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps } from 'vue';
import axios from 'axios';

interface Props {
  postedEmail?: string;
  entPoint: string;
}

const props = defineProps<Props>();

const password = ref<string>('');
const loading = ref<boolean>(false);

const getPassword = async () => {
  if (!props.entPoint || loading.value) return;

  loading.value = true;
  try {
    const response = await axios.get(props.entPoint);
    password.value = response.data;
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  getPassword();
});
</script>
