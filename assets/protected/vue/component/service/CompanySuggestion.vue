<template>
  <div class="row mb-3">
    <div class="col-md-2" v-html="$t('newregister/company')"></div>

    <div class="col-md-4">
      <input
        v-model="registedCompany"
        class="form-control"
        name="company"
        type="text"
        :class="error ? 'is-invalid' : ''"
        :placeholder="$t('newregister/company')" />
      <small v-if="error" class="small text-danger" v-html="error"></small>
    </div>

    <div class="col-md-6">
      <select v-model="selectedCompany" class="form-control" @change="company">
        <option v-for="company in companyList" :key="company.name + '_' + company.number" :value="company">
          {{ company.name }} ( {{ company.number }} )
        </option>
      </select>
    </div>

    <input v-if="selectedCompany.name !== ''" name="companySuggestion" type="hidden" :value="selectedCompany.name" />
    <input
      v-if="selectedCompany.number !== ''"
      name="companySuggestionNumber"
      type="hidden"
      :value="selectedCompany.number" />
    <input name="company" type="hidden" :value="registedCompany" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed, defineProps, defineExpose } from 'vue';
import { store } from '@assets/protected/vue/store';

type Company = {
  name: string;
  number: string;
};

type Registration = {
  company: string;
};

const props = defineProps<{
  companies: Record<string, Company> | Company[];
  registration: Registration;
}>();

const selectedCompany = ref<Company>({ name: '', number: '' });
const error = ref<string>('');
const registedCompany = ref<string>('');

const companyList = computed<Company[]>(() =>
  Array.isArray(props.companies) ? props.companies : Object.values(props.companies),
);

function checkIfNameLengthIsAuthorize(name: string) {
  if (!name) {
    error.value = '';
    return;
  }
  if (name.length > 65) {
    error.value = 'Der Name darf maximal 65 Zeichen lang sein!';
    registedCompany.value = name;
    return;
  }
  error.value = '';
}

watch(registedCompany, (val) => {
  checkIfNameLengthIsAuthorize(val);
});

function company() {
  store.dispatch('services/setCompany', selectedCompany.value);
  registedCompany.value = selectedCompany.value.name;
}

function getCompanyName() {
  return selectedCompany.value;
}
defineExpose({ getCompanyName });

onMounted(() => {
  checkIfNameLengthIsAuthorize(props.registration?.company || '');
  const targetName = (props.registration?.company || '').toUpperCase();

  // Determine initial selected company based on provided companies shape
  if (Array.isArray(props.companies)) {
    const found = props.companies.find((c) => (c?.name || '').toUpperCase() === targetName);
    selectedCompany.value = found ? found : { name: props.registration.company || '', number: '' };
  } else {
    if (Object.keys(props.companies).includes(targetName)) {
      selectedCompany.value = props.companies[targetName];
    } else {
      selectedCompany.value = { name: props.registration.company || '', number: '' };
    }
  }

  store.dispatch('services/setCompany', selectedCompany.value);
  registedCompany.value = selectedCompany.value.name;
});
</script>
