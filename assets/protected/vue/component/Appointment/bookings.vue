<template>
  <div id="bookingsTool">
    <vue-simple-spinner v-show="loading" size="big" :message="'loading...'"></vue-simple-spinner>
    <div v-if="show && !loading">
      <h4 v-show="bookings.length > 0"><strong v-html="category.niceName + ' - ' + event.name"></strong></h4>
      <sort-table :items="bookings" :fields="fieldsMap" :striped="true" :hover="true">
        <template #cell(aktionen)="row">
          <b-button
            v-if="row.item.Status === 'Offen'"
            class="mr-1"
            size="sm"
            @click="cancelItem(row.item.Aktionen)"
            variant="info">
            Stornieren
          </b-button>
          <b-button class="mr-1" size="sm" @click="deleteItem(row.item.Aktionen)" variant="danger"> Löschen </b-button>
        </template>
      </sort-table>
    </div>
    <div v-if="!show && !error" class="bs-callout bs-callout-info">
      Bitte eine Kategorie und Veranstaltung auswählen und dann den Button "Buchungen anzeigen" klicken.
    </div>
    <div v-if="error" class="bs-callout bs-callout-danger" v-html="error"></div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import Component from 'vue-class-component';
import SortTable from '@assets/protected/vue/component/helper/SortTable.vue';
import { Getter } from 'vuex-class';
let Spinner = require('vue-simple-spinner');

@Component({
  name: 'BookingsComponent',
  components: {
    SortTable,
    VueSimpleSpinner: Spinner,
  },
})
export default class Bookings extends Vue {
  @Getter('bookings/bookings') bookings: [];
  @Getter('bookings/showTable') show: false;
  @Getter('bookings/category') category: false;
  @Getter('bookings/event') event: false;
  @Getter('bookings/loading') loading: boolean;
  @Getter('bookings/error') error: string;

  cancel(id: number) {
    this.$store.dispatch('bookings/cancelBooking', id);
  }
  cancelItem(id: number) {
    this.$bvModal
      .msgBoxConfirm('Möchten Sie wirklich stornieren ?', {
        title: 'Stornieren',
        size: 'sm',
        buttonSize: 'sm',
        okVariant: 'danger',
        okTitle: 'Ja',
        cancelTitle: 'Nein',
        footerClass: 'p-2',
        hideHeaderClose: false,
        centered: true,
      })
      .then((value) => {
        if (value) {
          this.$store.dispatch('bookings/cancelBooking', id);
        }
      })
      .catch((err) => {});
  }

  deleteItem(id: number) {
    this.$bvModal
      .msgBoxConfirm('Möchten Sie wirklich löschen ?', {
        title: 'Löschen',
        size: 'sm',
        buttonSize: 'sm',
        okVariant: 'danger',
        okTitle: 'Ja',
        cancelTitle: 'Nein',
        footerClass: 'p-2',
        hideHeaderClose: false,
        centered: true,
      })
      .then((value) => {
        if (value) {
          this.$store.dispatch('bookings/deleteBooking', id);
        }
      })
      .catch((err) => {});
  }
  get fieldsMap() {
    let keys: any = [];
    if (Object.keys(this.$store.getters['bookings/bookings']).length > 0) {
      Object.keys(this.$store.getters['bookings/bookings'][0]).forEach(function (key: string) {
        keys.push({ key: key, sortable: true });
      });
    }
    return keys;
  }
}
</script>
