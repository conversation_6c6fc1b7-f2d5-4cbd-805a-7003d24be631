FROM nextcloud:30.0.4-fpm

ARG cert
ARG environment
ENV environment="$environment"

### FOR DEVELOPMENT ONLY
ARG development
#RUN if [ "$development" -eq "1" ]; then pear config-set http_proxy $http_proxy; fi
#RUN if [ "$development" -eq "1" ]; then echo "Acquire::http::Pipeline-Depth 0;" >> /etc/apt/apt.conf.d/99fixbadproxy; fi
#RUN if [ "$development" -eq "1" ]; then echo 'Acquire::http::No-Cache true;' >> /etc/apt/apt.conf.d/99fixbadproxy; fi
#RUN if [ "$development" -eq "1" ]; then echo "Acquire::BrokenProxy true;" >> /etc/apt/apt.conf.d/99fixbadproxy; fi

RUN echo "upload_max_filesize = 2048M" >> /usr/local/etc/php/php.ini
RUN echo "post_max_size = 2048M" >> /usr/local/etc/php/php.ini
RUN echo "max_execution_time = 300" >> /usr/local/etc/php/php.ini 

# LDAP konfigurieren
COPY nextcloud/config/ldap.conf /etc/ldap/ldap.conf
RUN chmod 774 /etc/ldap/ldap.conf
COPY nextcloud/config/abus_ldap.cert abus_ldap.cert
RUN cat abus_ldap.cert >> /etc/ssl/certs/ca-certificates.crt
RUN rm abus_ldap.cert
RUN chmod 774 /etc/ssl/certs/ca-certificates.crt

RUN apt-get clean && apt-get update && apt-get install -y \
        sudo \
        smbclient \
        vim \
        cron\
        imagemagick \
    && rm -rf /var/lib/apt/lists/*

RUN apt-get update && apt-get install -y libmagickwand-dev --no-install-recommends && rm -rf /var/lib/apt/lists/*
RUN docker-php-ext-enable imagick bcmath

# PHP-FPM konfigurieren
COPY nextcloud/config/php-fpm.conf /usr/local/etc/php-fpm.d/z_last_abus.conf

# Add CRONJOB
RUN mkdir /var/log/cron/ && touch /var/log/cron/nextcloud.log && chmod 777 /var/log/cron/nextcloud.log
COPY environment/$environment/cron /etc/cron.d/nextcloud-cron
RUN chmod 0644 /etc/cron.d/nextcloud-cron
RUN crontab /etc/cron.d/nextcloud-cron

# WEBDAV Zertifikat kopieren
#COPY environment/$environment/serverRootCA.pem /etc/ssl/serverRootCA.pem
COPY nextcloud/config/webdav.cer /etc/ssl/webdav.crt
COPY nextcloud/config/abus_root.cer /etc/ssl/abus_root.crt
COPY nextcloud/config/abus_ca.cer /etc/ssl/abus_ca.crt

### FOR DEVELOPMENT ONLY | Install xDebug
#RUN if [ "$development" -eq "1" ]; then yes | pecl install xdebug && mkdir /var/log/xdebug && echo "zend_extension=$(find /usr/local/lib/php/extensions/ -name xdebug.so)" > /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.default_enable=0" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.remote_enable=1" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.remote_autostart=0" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.profiler_enable=0" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.profiler_output_dir=/var/log/xdebug" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.remote_log=/var/log/xdebug/remote.log" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.remote_connect_back=0" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.remote_port=9000" >> /usr/local/etc/php/conf.d/xdebug.ini && echo "xdebug.idekey=PHPSTORM" >> /usr/local/etc/php/conf.d/xdebug.ini; fi

# FullTextSearch
RUN /usr/bin/php /var/www/html/occ fulltextsearch:live &

COPY nextcloud/entrypoint.sh /
RUN chmod 777 /entrypoint.sh
ENTRYPOINT /entrypoint.sh
