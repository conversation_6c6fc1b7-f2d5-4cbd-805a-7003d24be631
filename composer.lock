{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "7347d218668fe3abb0c9448440b68985", "packages": [{"name": "doctrine/cache", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/1ca8f21980e770095a31456042471a57bc4c68fb", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:07:39+00:00"}, {"name": "doctrine/collections", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/2eb07e5953eed811ce1b309a7478a3b236f2273d", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d", "shasum": ""}, "require": {"doctrine/deprecations": "^1", "php": "^8.1", "symfony/polyfill-php84": "^1.30"}, "require-dev": {"doctrine/coding-standard": "^12", "ext-json": "*", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^10.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.3.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcollections", "type": "tidelift"}], "time": "2025-03-22T10:17:19+00:00"}, {"name": "doctrine/dbal", "version": "4.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "33d2d7fe1269b2301640c44cf2896ea607b30e3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/33d2d7fe1269b2301640c44cf2896ea607b30e3e", "reference": "33d2d7fe1269b2301640c44cf2896ea607b30e3e", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3|^1", "php": "^8.1", "psr/cache": "^1|^2|^3", "psr/log": "^1|^2|^3"}, "require-dev": {"doctrine/coding-standard": "12.0.0", "fig/log-test": "^1", "jetbrains/phpstorm-stubs": "2023.2", "phpstan/phpstan": "2.1.1", "phpstan/phpstan-phpunit": "2.0.3", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "10.5.39", "slevomat/coding-standard": "8.13.1", "squizlabs/php_codesniffer": "3.10.2", "symfony/cache": "^6.3.8|^7.0", "symfony/console": "^5.4|^6.3|^7.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/4.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2025-03-07T18:29:05+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/doctrine-bundle", "version": "2.13.3", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineBundle.git", "reference": "aac7562c96d117e16cbadfe41bef17d2fc760f74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineBundle/zipball/aac7562c96d117e16cbadfe41bef17d2fc760f74", "reference": "aac7562c96d117e16cbadfe41bef17d2fc760f74", "shasum": ""}, "require": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/dbal": "^3.7.0 || ^4.0", "doctrine/persistence": "^2.2 || ^3", "doctrine/sql-formatter": "^1.0.1", "php": "^7.4 || ^8.0", "symfony/cache": "^5.4 || ^6.0 || ^7.0", "symfony/config": "^5.4 || ^6.0 || ^7.0", "symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/dependency-injection": "^5.4 || ^6.0 || ^7.0", "symfony/deprecation-contracts": "^2.1 || ^3", "symfony/doctrine-bridge": "^5.4.46 || ~6.3.12 || ^6.4.3 || ^7.0.3", "symfony/framework-bundle": "^5.4 || ^6.0 || ^7.0", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1.1 || ^2.0 || ^3"}, "conflict": {"doctrine/annotations": ">=3.0", "doctrine/orm": "<2.17 || >=4.0", "twig/twig": "<1.34 || >=2.0 <2.4"}, "require-dev": {"doctrine/annotations": "^1 || ^2", "doctrine/coding-standard": "^12", "doctrine/deprecations": "^1.0", "doctrine/orm": "^2.17 || ^3.0", "friendsofphp/proxy-manager-lts": "^1.0", "phpstan/phpstan": "2.1.1", "phpstan/phpstan-phpunit": "2.0.3", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^9.5.26", "psr/log": "^1.1.4 || ^2.0 || ^3.0", "symfony/phpunit-bridge": "^6.1 || ^7.0", "symfony/property-info": "^5.4 || ^6.0 || ^7.0", "symfony/proxy-manager-bridge": "^5.4 || ^6.0", "symfony/security-bundle": "^5.4 || ^6.0 || ^7.0", "symfony/stopwatch": "^5.4 || ^6.0 || ^7.0", "symfony/string": "^5.4 || ^6.0 || ^7.0", "symfony/twig-bridge": "^5.4 || ^6.0 || ^7.0", "symfony/validator": "^5.4 || ^6.0 || ^7.0", "symfony/var-exporter": "^5.4 || ^6.2 || ^7.0", "symfony/web-profiler-bundle": "^5.4 || ^6.0 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0", "twig/twig": "^1.34 || ^2.12 || ^3.0"}, "suggest": {"doctrine/orm": "The Doctrine ORM integration is optional in the bundle.", "ext-pdo": "*", "symfony/web-profiler-bundle": "To use the data collector."}, "type": "symfony-bundle", "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "https://www.doctrine-project.org/"}], "description": "Symfony DoctrineBundle", "homepage": "https://www.doctrine-project.org", "keywords": ["database", "dbal", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/DoctrineBundle/issues", "source": "https://github.com/doctrine/DoctrineBundle/tree/2.13.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdoctrine-bundle", "type": "tidelift"}], "time": "2025-03-16T10:55:20+00:00"}, {"name": "doctrine/doctrine-migrations-bundle", "version": "3.4.1", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineMigrationsBundle.git", "reference": "e858ce0f5c12b266dce7dce24834448355155da7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineMigrationsBundle/zipball/e858ce0f5c12b266dce7dce24834448355155da7", "reference": "e858ce0f5c12b266dce7dce24834448355155da7", "shasum": ""}, "require": {"doctrine/doctrine-bundle": "^2.4", "doctrine/migrations": "^3.2", "php": "^7.2 || ^8.0", "symfony/deprecation-contracts": "^2.1 || ^3", "symfony/framework-bundle": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"composer/semver": "^3.0", "doctrine/coding-standard": "^12", "doctrine/orm": "^2.6 || ^3", "doctrine/persistence": "^2.0 || ^3", "phpstan/phpstan": "^1.4 || ^2", "phpstan/phpstan-deprecation-rules": "^1 || ^2", "phpstan/phpstan-phpunit": "^1 || ^2", "phpstan/phpstan-strict-rules": "^1.1 || ^2", "phpstan/phpstan-symfony": "^1.3 || ^2", "phpunit/phpunit": "^8.5 || ^9.5", "symfony/phpunit-bridge": "^6.3 || ^7", "symfony/var-exporter": "^5.4 || ^6 || ^7"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Doctrine\\Bundle\\MigrationsBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Doctrine Project", "homepage": "https://www.doctrine-project.org"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DoctrineMigrationsBundle", "homepage": "https://www.doctrine-project.org", "keywords": ["dbal", "migrations", "schema"], "support": {"issues": "https://github.com/doctrine/DoctrineMigrationsBundle/issues", "source": "https://github.com/doctrine/DoctrineMigrationsBundle/tree/3.4.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdoctrine-migrations-bundle", "type": "tidelift"}], "time": "2025-01-27T22:48:22+00:00"}, {"name": "doctrine/event-manager", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/b680156fa328f1dfd874fd48c7026c41570b9c6e", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^10.5", "vimeo/psalm": "^5.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/2.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2024-05-22T20:47:39+00:00"}, {"name": "doctrine/inflector", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/5817d0659c5b50c9b950feb9af7b9668e2c436bc", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.10"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2024-02-18T20:23:39+00:00"}, {"name": "doctrine/instantiator", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/0a0fa9780f5d4e507415a065172d26a98d02047b", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.30 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.5.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:15:36+00:00"}, {"name": "doctrine/lexer", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:56:58+00:00"}, {"name": "doctrine/migrations", "version": "3.9.0", "source": {"type": "git", "url": "https://github.com/doctrine/migrations.git", "reference": "325b61e41d032f5f7d7e2d11cbefff656eadc9ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/migrations/zipball/325b61e41d032f5f7d7e2d11cbefff656eadc9ab", "reference": "325b61e41d032f5f7d7e2d11cbefff656eadc9ab", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/dbal": "^3.6 || ^4", "doctrine/deprecations": "^0.5.3 || ^1", "doctrine/event-manager": "^1.2 || ^2.0", "php": "^8.1", "psr/log": "^1.1.3 || ^2 || ^3", "symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/stopwatch": "^5.4 || ^6.0 || ^7.0", "symfony/var-exporter": "^6.2 || ^7.0"}, "conflict": {"doctrine/orm": "<2.12 || >=4"}, "require-dev": {"doctrine/coding-standard": "^12", "doctrine/orm": "^2.13 || ^3", "doctrine/persistence": "^2 || ^3 || ^4", "doctrine/sql-formatter": "^1.0", "ext-pdo_sqlite": "*", "fig/log-test": "^1", "phpstan/phpstan": "^1.10", "phpstan/phpstan-deprecation-rules": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpstan/phpstan-strict-rules": "^1.4", "phpstan/phpstan-symfony": "^1.3", "phpunit/phpunit": "^10.3", "symfony/cache": "^5.4 || ^6.0 || ^7.0", "symfony/process": "^5.4 || ^6.0 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0"}, "suggest": {"doctrine/sql-formatter": "Allows to generate formatted SQL with the diff command.", "symfony/yaml": "Allows the use of yaml for migration configuration files."}, "bin": ["bin/doctrine-migrations"], "type": "library", "autoload": {"psr-4": {"Doctrine\\Migrations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Migrations project offer additional functionality on top of the database abstraction layer (DBAL) for versioning your database schema and easily deploying changes to it. It is a very easy to use and a powerful tool.", "homepage": "https://www.doctrine-project.org/projects/migrations.html", "keywords": ["database", "dbal", "migrations"], "support": {"issues": "https://github.com/doctrine/migrations/issues", "source": "https://github.com/doctrine/migrations/tree/3.9.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fmigrations", "type": "tidelift"}], "time": "2025-03-26T06:48:45+00:00"}, {"name": "doctrine/orm", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/doctrine/orm.git", "reference": "c9557c588b3a70ed93caff069d0aa75737f25609"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/orm/zipball/c9557c588b3a70ed93caff069d0aa75737f25609", "reference": "c9557c588b3a70ed93caff069d0aa75737f25609", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/collections": "^2.2", "doctrine/dbal": "^3.8.2 || ^4", "doctrine/deprecations": "^0.5.3 || ^1", "doctrine/event-manager": "^1.2 || ^2", "doctrine/inflector": "^1.4 || ^2.0", "doctrine/instantiator": "^1.3 || ^2", "doctrine/lexer": "^3", "doctrine/persistence": "^3.3.1 || ^4", "ext-ctype": "*", "php": "^8.1", "psr/cache": "^1 || ^2 || ^3", "symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/var-exporter": "^6.3.9 || ^7.0"}, "require-dev": {"doctrine/coding-standard": "^12.0", "phpbench/phpbench": "^1.0", "phpdocumentor/guides-cli": "^1.4", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "2.0.3", "phpstan/phpstan-deprecation-rules": "^2", "phpunit/phpunit": "^10.4.0", "psr/log": "^1 || ^2 || ^3", "squizlabs/php_codesniffer": "3.7.2", "symfony/cache": "^5.4 || ^6.2 || ^7.0"}, "suggest": {"ext-dom": "Provides support for XSD validation for XML mapping files", "symfony/cache": "Provides cache support for Setup Tool with doctrine/cache 2.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\ORM\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Object-Relational-Mapper for PHP", "homepage": "https://www.doctrine-project.org/projects/orm.html", "keywords": ["database", "orm"], "support": {"issues": "https://github.com/doctrine/orm/issues", "source": "https://github.com/doctrine/orm/tree/3.3.2"}, "time": "2025-02-04T19:43:15+00:00"}, {"name": "doctrine/persistence", "version": "3.4.0", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "0ea965320cec355dba75031c1b23d4c78362e3ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/0ea965320cec355dba75031c1b23d4c78362e3ff", "reference": "0ea965320cec355dba75031c1b23d4c78362e3ff", "shasum": ""}, "require": {"doctrine/event-manager": "^1 || ^2", "php": "^7.2 || ^8.0", "psr/cache": "^1.0 || ^2.0 || ^3.0"}, "conflict": {"doctrine/common": "<2.10"}, "require-dev": {"doctrine/coding-standard": "^12", "doctrine/common": "^3.0", "phpstan/phpstan": "1.12.7", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5.38 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Persistence\\": "src/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://www.doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.4.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2024-10-30T19:48:12+00:00"}, {"name": "doctrine/sql-formatter", "version": "1.5.2", "source": {"type": "git", "url": "https://github.com/doctrine/sql-formatter.git", "reference": "d6d00aba6fd2957fe5216fe2b7673e9985db20c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/sql-formatter/zipball/d6d00aba6fd2957fe5216fe2b7673e9985db20c8", "reference": "d6d00aba6fd2957fe5216fe2b7673e9985db20c8", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "ergebnis/phpunit-slow-test-detector": "^2.14", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5"}, "bin": ["bin/sql-formatter"], "type": "library", "autoload": {"psr-4": {"Doctrine\\SqlFormatter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/doctrine/sql-formatter/", "keywords": ["highlight", "sql"], "support": {"issues": "https://github.com/doctrine/sql-formatter/issues", "source": "https://github.com/doctrine/sql-formatter/tree/1.5.2"}, "time": "2025-01-24T11:45:48+00:00"}, {"name": "egulias/email-validator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.4"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2025-03-06T22:45:56+00:00"}, {"name": "firebase/php-jwt", "version": "v6.11.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "d1e91ecf8c598d073d0995afa8cd5c75c6e19e66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/d1e91ecf8c598d073d0995afa8cd5c75c6e19e66", "reference": "d1e91ecf8c598d073d0995afa8cd5c75c6e19e66", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.4", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psr/cache": "^2.0||^3.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.11.1"}, "time": "2025-04-09T20:32:01+00:00"}, {"name": "graylog2/gelf-php", "version": "1.7.1", "source": {"type": "git", "url": "https://github.com/bzikarsky/gelf-php.git", "reference": "8dceab86227c184725479cc36ab5cae4da940f6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bzi<PERSON>sky/gelf-php/zipball/8dceab86227c184725479cc36ab5cae4da940f6e", "reference": "8dceab86227c184725479cc36ab5cae4da940f6e", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2", "php": ">=5.6", "psr/log": "^1.0|^2.0"}, "provide": {"psr/log-implementation": "~1.0"}, "require-dev": {"phpunit/phpunit": "^5.7|^6.5|^7.5", "squizlabs/php_codesniffer": "^3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"Gelf\\": "src/Gelf"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "gelf-php contributors", "homepage": "https://github.com/bzi<PERSON>sky/gelf-php/contributors"}], "description": "A php implementation to send log-messages to a GELF compatible backend like Graylog2.", "support": {"issues": "https://github.com/bzikarsky/gelf-php/issues", "source": "https://github.com/bzikarsky/gelf-php/tree/1.7.1"}, "time": "2021-08-20T09:39:08+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-03-27T13:37:11+00:00"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2025-03-27T13:27:01+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "knplabs/knp-snappy", "version": "v1.5.1", "source": {"type": "git", "url": "https://github.com/KnpLabs/snappy.git", "reference": "3dd138e9e47de91cd2e056c5e6e1a0dd72547ee7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/snappy/zipball/3dd138e9e47de91cd2e056c5e6e1a0dd72547ee7", "reference": "3dd138e9e47de91cd2e056c5e6e1a0dd72547ee7", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0||^3.0", "symfony/process": "^5.0||^6.0||^7.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "pedrotroller/php-cs-custom-fixer": "^2.19", "phpstan/phpstan": "^1.0.0", "phpstan/phpstan-phpunit": "^1.0.0", "phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Knp\\Snappy\\": "src/Knp/Snappy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KNP Labs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/snappy/contributors"}], "description": "PHP library allowing thumbnail, snapshot or PDF generation from a url or a html page. Wrapper for wkhtmltopdf/wkhtmltoimage.", "homepage": "http://github.com/KnpLabs/snappy", "keywords": ["knp", "knplabs", "pdf", "snapshot", "thumbnail", "wkhtmltopdf"], "support": {"issues": "https://github.com/KnpLabs/snappy/issues", "source": "https://github.com/KnpLabs/snappy/tree/v1.5.1"}, "time": "2025-01-06T16:53:26+00:00"}, {"name": "knplabs/knp-snappy-bundle", "version": "v1.10.4", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpSnappyBundle.git", "reference": "df217e5deed3397de88b7eb96de1d7c0cd7ed9e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpSnappyBundle/zipball/df217e5deed3397de88b7eb96de1d7c0cd7ed9e9", "reference": "df217e5deed3397de88b7eb96de1d7c0cd7ed9e9", "shasum": ""}, "require": {"knplabs/knp-snappy": "^1.4.3", "php": ">=8.1", "symfony/framework-bundle": "^5.1|^6.0|^7.0"}, "require-dev": {"phpunit/phpunit": "^8.5", "symfony/yaml": "^5.1|^6.0|^7.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Knp\\Bundle\\SnappyBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/KnpSnappyBundle/contributors"}], "description": "Easily create PDF and images in Symfony by converting Twig/HTML templates.", "homepage": "http://github.com/KnpLabs/KnpSnappyBundle", "keywords": ["bundle", "knp", "knplabs", "pdf", "snappy"], "support": {"issues": "https://github.com/KnpLabs/KnpSnappyBundle/issues", "source": "https://github.com/KnpLabs/KnpSnappyBundle/tree/v1.10.4"}, "time": "2025-01-14T07:31:54+00:00"}, {"name": "league/html-to-markdown", "version": "5.1.1", "source": {"type": "git", "url": "https://github.com/thephpleague/html-to-markdown.git", "reference": "0b4066eede55c48f38bcee4fb8f0aa85654390fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/html-to-markdown/zipball/0b4066eede55c48f38bcee4fb8f0aa85654390fd", "reference": "0b4066eede55c48f38bcee4fb8f0aa85654390fd", "shasum": ""}, "require": {"ext-dom": "*", "ext-xml": "*", "php": "^7.2.5 || ^8.0"}, "require-dev": {"mikehaertl/php-shellcommand": "^1.1.0", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^8.5 || ^9.2", "scrutinizer/ocular": "^1.6", "unleashedtech/php-coding-standard": "^2.7 || ^3.0", "vimeo/psalm": "^4.22 || ^5.0"}, "bin": ["bin/html-to-markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}, "autoload": {"psr-4": {"League\\HTMLToMarkdown\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://modernnerd.net", "role": "Original Author"}], "description": "An HTML-to-markdown conversion helper for PHP", "homepage": "https://github.com/thephpleague/html-to-markdown", "keywords": ["html", "markdown"], "support": {"issues": "https://github.com/thephpleague/html-to-markdown/issues", "source": "https://github.com/thephpleague/html-to-markdown/tree/5.1.1"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/html-to-markdown", "type": "tidelift"}], "time": "2023-07-12T21:21:09+00:00"}, {"name": "league/oauth2-client", "version": "2.8.1", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-client.git", "reference": "9df2924ca644736c835fc60466a3a60390d334f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-client/zipball/9df2924ca644736c835fc60466a3a60390d334f9", "reference": "9df2924ca644736c835fc60466a3a60390d334f9", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^6.5.8 || ^7.4.5", "php": "^7.1 || >=8.0.0 <8.5.0"}, "require-dev": {"mockery/mockery": "^1.3.5", "php-parallel-lint/php-parallel-lint": "^1.4", "phpunit/phpunit": "^7 || ^8 || ^9 || ^10 || ^11", "squizlabs/php_codesniffer": "^3.11"}, "type": "library", "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/shadowhand", "role": "Contributor"}], "description": "OAuth 2.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "identity", "idp", "o<PERSON>h", "oauth2", "single sign on"], "support": {"issues": "https://github.com/thephpleague/oauth2-client/issues", "source": "https://github.com/thephpleague/oauth2-client/tree/2.8.1"}, "time": "2025-02-26T04:37:30+00:00"}, {"name": "mainick/keycloak-client-bundle", "version": "2.2.3", "source": {"type": "git", "url": "https://github.com/mainick/KeycloakClientBundle.git", "reference": "9a89f07b62dab8bdd6a85a16a64152fd96f84480"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mainick/KeycloakClientBundle/zipball/9a89f07b62dab8bdd6a85a16a64152fd96f84480", "reference": "9a89f07b62dab8bdd6a85a16a64152fd96f84480", "shasum": ""}, "require": {"php": ">=8.2", "stevenmaguire/oauth2-keycloak": "^5.1", "symfony/framework-bundle": "^7.1", "symfony/http-kernel": "^7.1", "symfony/routing": "^7.1", "symfony/security-bundle": "^7.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.59", "mockery/mockery": "~1.5.0", "phpunit/phpunit": "^11.2"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Mainick\\KeycloakClientBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mainick"}], "description": "Keycloak client bundle for Symfony, designed to simplify Keycloak integration into your application and provide additional functionality for token management and user information access", "keywords": ["jwt-token", "keycloak-client", "oauth2-keycloak"], "support": {"issues": "https://github.com/mainick/KeycloakClientBundle/issues", "source": "https://github.com/mainick/KeycloakClientBundle/tree/2.2.3"}, "time": "2024-11-24T11:52:23+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "time": "2024-03-31T07:05:07+00:00"}, {"name": "maxmind-db/reader", "version": "v1.12.0", "source": {"type": "git", "url": "https://github.com/maxmind/MaxMind-DB-Reader-php.git", "reference": "5b2d7a721dedfaef9dc20822c5fe7d26f9f8eb90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/MaxMind-DB-Reader-php/zipball/5b2d7a721dedfaef9dc20822c5fe7d26f9f8eb90", "reference": "5b2d7a721dedfaef9dc20822c5fe7d26f9f8eb90", "shasum": ""}, "require": {"php": ">=7.2"}, "conflict": {"ext-maxminddb": "<1.11.1 || >=2.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.*", "phpstan/phpstan": "*", "phpunit/phpunit": ">=8.0.0,<10.0.0", "squizlabs/php_codesniffer": "3.*"}, "suggest": {"ext-bcmath": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-gmp": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-maxminddb": "A C-based database decoder that provides significantly faster lookups"}, "type": "library", "autoload": {"psr-4": {"MaxMind\\Db\\": "src/MaxMind/Db"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.maxmind.com/"}], "description": "MaxMind DB Reader API", "homepage": "https://github.com/maxmind/MaxMind-DB-Reader-php", "keywords": ["database", "geoip", "geoip2", "geolocation", "maxmind"], "support": {"issues": "https://github.com/maxmind/MaxMind-DB-Reader-php/issues", "source": "https://github.com/maxmind/MaxMind-DB-Reader-php/tree/v1.12.0"}, "time": "2024-11-14T22:43:47+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.45", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "96aaebcf4f50d3d2692ab81d2c5132e425bca266"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/96aaebcf4f50d3d2692ab81d2c5132e425bca266", "reference": "96aaebcf4f50d3d2692ab81d2c5132e425bca266", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36"}, "type": "library", "autoload": {"psr-0": {"Detection": "namespaced/"}, "classmap": ["Mobile_Detect.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "support": {"issues": "https://github.com/serbanghita/Mobile-Detect/issues", "source": "https://github.com/serbanghita/Mobile-Detect/tree/2.8.45"}, "funding": [{"url": "https://github.com/serbanghita", "type": "github"}], "time": "2023-11-07T21:57:25+00:00"}, {"name": "monolog/monolog", "version": "3.9.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/10d85740180ecba7896c87e06a166e0c95a0e3b6", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.8", "phpstan/phpstan": "^2", "phpstan/phpstan-deprecation-rules": "^2", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^10.5.17 || ^11.0.7", "predis/predis": "^1.1 || ^2", "rollbar/rollbar": "^4.0", "ruflin/elastica": "^7 || ^8", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.9.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2025-03-24T10:02:05+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v2.7.0", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "52a0d99e69f56b9ec27ace92ba56897fe6993105"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/52a0d99e69f56b9ec27ace92ba56897fe6993105", "reference": "52a0d99e69f56b9ec27ace92ba56897fe6993105", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2024-05-08T12:18:48+00:00"}, {"name": "pentatrion/vite-bundle", "version": "v8.2.1", "source": {"type": "git", "url": "https://github.com/lhapaipai/vite-bundle.git", "reference": "be23b5ea89e38a68d4db787333c4d592d8a5020f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lhapaipai/vite-bundle/zipball/be23b5ea89e38a68d4db787333c4d592d8a5020f", "reference": "be23b5ea89e38a68d4db787333c4d592d8a5020f", "shasum": ""}, "require": {"php": "^8.0", "symfony/asset": "^4.4 || ^5.0 || ^6.0 || ^7.0", "symfony/config": "^4.4 || ^5.0 || ^6.0 || ^7.0", "symfony/dependency-injection": "^4.4 || ^5.0 || ^6.0 || ^7.0", "symfony/framework-bundle": "^4.4 || ^5.0 || ^6.0 || ^7.0", "symfony/http-client": "^4.4 || ^5.0 || ^6.0 || ^7.0", "symfony/http-kernel": "^4.4 || ^5.0 || ^6.0 || ^7.0", "symfony/twig-bundle": "^4.4 || ^5.0 || ^6.0 || ^7.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.9", "phpstan/phpstan": "^1.8", "phpstan/phpstan-symfony": "^1.3", "phpunit/phpunit": "^9.5", "symfony/phpunit-bridge": "^4.4 || ^5.0 || ^6.0 || ^7.0", "symfony/web-link": "^4.4 || ^5.0 || ^6.0 || ^7.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Pentatrion\\ViteBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Vite integration for your Symfony app", "keywords": ["bundle", "symfony", "vite", "vite<PERSON><PERSON>"], "support": {"issues": "https://github.com/lhapaipai/vite-bundle/issues", "source": "https://github.com/lhapaipai/vite-bundle/tree/v8.2.1"}, "time": "2025-08-16T20:29:55+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.6.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "e5e784149a09bd69d9a5e3b01c5cbd2e2bd653d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/e5e784149a09bd69d9a5e3b01c5cbd2e2bd653d8", "reference": "e5e784149a09bd69d9a5e3b01c5cbd2e2bd653d8", "shasum": ""}, "require": {"doctrine/deprecations": "^1.1", "ext-filter": "*", "php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.7", "phpstan/phpdoc-parser": "^1.7|^2.0", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.5 || ~1.6.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^9.5", "psalm/phar": "^5.26"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.6.1"}, "time": "2024-12-07T09:39:29+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/679e3ce485b99e84c775d28e2e96fade9a7fb50a", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.18|^2.0"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.10.0"}, "time": "2024-11-09T15:12:26+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^5.3.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.6", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/2.1.0"}, "time": "2025-02-19T13:28:12+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "ef29f6d262798707a9edd554e2b82517ef3a9376"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/ef29f6d262798707a9edd554e2b82517ef3a9376", "reference": "ef29f6d262798707a9edd554e2b82517ef3a9376", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/2.0.0"}, "time": "2021-07-14T16:41:46+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "sabre/dav", "version": "4.7.0", "source": {"type": "git", "url": "https://github.com/sabre-io/dav.git", "reference": "074373bcd689a30bcf5aaa6bbb20a3395964ce7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/dav/zipball/074373bcd689a30bcf5aaa6bbb20a3395964ce7a", "reference": "074373bcd689a30bcf5aaa6bbb20a3395964ce7a", "shasum": ""}, "require": {"ext-ctype": "*", "ext-date": "*", "ext-dom": "*", "ext-iconv": "*", "ext-json": "*", "ext-mbstring": "*", "ext-pcre": "*", "ext-simplexml": "*", "ext-spl": "*", "lib-libxml": ">=2.7.0", "php": "^7.1.0 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "sabre/event": "^5.0", "sabre/http": "^5.0.5", "sabre/uri": "^2.0", "sabre/vobject": "^4.2.1", "sabre/xml": "^2.0.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.19", "monolog/monolog": "^1.27 || ^2.0", "phpstan/phpstan": "^0.12 || ^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6"}, "suggest": {"ext-curl": "*", "ext-imap": "*", "ext-pdo": "*"}, "bin": ["bin/sabredav", "bin/naturalselection"], "type": "library", "autoload": {"psr-4": {"Sabre\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}], "description": "WebDAV Framework for PHP", "homepage": "http://sabre.io/", "keywords": ["CalDAV", "CardDAV", "WebDAV", "framework", "iCalendar"], "support": {"forum": "https://groups.google.com/group/sabredav-discuss", "issues": "https://github.com/sabre-io/dav/issues", "source": "https://github.com/fruux/sabre-dav"}, "time": "2024-10-29T11:46:02+00:00"}, {"name": "sabre/event", "version": "5.1.7", "source": {"type": "git", "url": "https://github.com/sabre-io/event.git", "reference": "86d57e305c272898ba3c28e9bd3d65d5464587c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/event/zipball/86d57e305c272898ba3c28e9bd3d65d5464587c2", "reference": "86d57e305c272898ba3c28e9bd3d65d5464587c2", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.17.1||^3.63", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6"}, "type": "library", "autoload": {"files": ["lib/coroutine.php", "lib/Loop/functions.php", "lib/Promise/functions.php"], "psr-4": {"Sabre\\Event\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}], "description": "sabre/event is a library for lightweight event-based programming", "homepage": "http://sabre.io/event/", "keywords": ["EventEmitter", "async", "coroutine", "eventloop", "events", "hooks", "plugin", "promise", "reactor", "signal"], "support": {"forum": "https://groups.google.com/group/sabredav-discuss", "issues": "https://github.com/sabre-io/event/issues", "source": "https://github.com/fruux/sabre-event"}, "time": "2024-08-27T11:23:05+00:00"}, {"name": "sabre/http", "version": "5.1.12", "source": {"type": "git", "url": "https://github.com/sabre-io/http.git", "reference": "dedff73f3995578bc942fa4c8484190cac14f139"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/http/zipball/dedff73f3995578bc942fa4c8484190cac14f139", "reference": "dedff73f3995578bc942fa4c8484190cac14f139", "shasum": ""}, "require": {"ext-ctype": "*", "ext-curl": "*", "ext-mbstring": "*", "php": "^7.1 || ^8.0", "sabre/event": ">=4.0 <6.0", "sabre/uri": "^2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.17.1||^3.63", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6"}, "suggest": {"ext-curl": " to make http requests with the Client class"}, "type": "library", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Sabre\\HTTP\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}], "description": "The sabre/http library provides utilities for dealing with http requests and responses. ", "homepage": "https://github.com/fruux/sabre-http", "keywords": ["http"], "support": {"forum": "https://groups.google.com/group/sabredav-discuss", "issues": "https://github.com/sabre-io/http/issues", "source": "https://github.com/fruux/sabre-http"}, "time": "2024-08-27T16:07:41+00:00"}, {"name": "sabre/uri", "version": "2.3.4", "source": {"type": "git", "url": "https://github.com/sabre-io/uri.git", "reference": "b76524c22de90d80ca73143680a8e77b1266c291"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/uri/zipball/b76524c22de90d80ca73143680a8e77b1266c291", "reference": "b76524c22de90d80ca73143680a8e77b1266c291", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.63", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^1.12", "phpstan/phpstan-phpunit": "^1.4", "phpstan/phpstan-strict-rules": "^1.6", "phpunit/phpunit": "^9.6"}, "type": "library", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Sabre\\Uri\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}], "description": "Functions for making sense out of URIs.", "homepage": "http://sabre.io/uri/", "keywords": ["rfc3986", "uri", "url"], "support": {"forum": "https://groups.google.com/group/sabredav-discuss", "issues": "https://github.com/sabre-io/uri/issues", "source": "https://github.com/fruux/sabre-uri"}, "time": "2024-08-27T12:18:16+00:00"}, {"name": "sabre/vobject", "version": "4.5.6", "source": {"type": "git", "url": "https://github.com/sabre-io/vobject.git", "reference": "900266bb3bd448a9f7f41f82344ad0aba237cb27"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/vobject/zipball/900266bb3bd448a9f7f41f82344ad0aba237cb27", "reference": "900266bb3bd448a9f7f41f82344ad0aba237cb27", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 || ^8.0", "sabre/xml": "^2.1 || ^3.0 || ^4.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.17.1", "phpstan/phpstan": "^0.12 || ^1.11", "phpunit/php-invoker": "^2.0 || ^3.1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6"}, "suggest": {"hoa/bench": "If you would like to run the benchmark scripts"}, "bin": ["bin/vobject", "bin/generate_vcards"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Sabre\\VObject\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://tobschall.de/", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://mnt.io/", "role": "Developer"}], "description": "The VObject library for PHP allows you to easily parse and manipulate iCalendar and vCard objects", "homepage": "http://sabre.io/vobject/", "keywords": ["availability", "freebusy", "iCalendar", "ical", "ics", "jCal", "jCard", "recurrence", "rfc2425", "rfc2426", "rfc2739", "rfc4770", "rfc5545", "rfc5546", "rfc6321", "rfc6350", "rfc6351", "rfc6474", "rfc6638", "rfc6715", "rfc6868", "vCalendar", "vCard", "vcf", "xCal", "xCard"], "support": {"forum": "https://groups.google.com/group/sabredav-discuss", "issues": "https://github.com/sabre-io/vobject/issues", "source": "https://github.com/fruux/sabre-vobject"}, "time": "2024-10-14T11:53:54+00:00"}, {"name": "sabre/xml", "version": "2.2.11", "source": {"type": "git", "url": "https://github.com/sabre-io/xml.git", "reference": "01a7927842abf3e10df3d9c2d9b0cc9d813a3fcc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/xml/zipball/01a7927842abf3e10df3d9c2d9b0cc9d813a3fcc", "reference": "01a7927842abf3e10df3d9c2d9b0cc9d813a3fcc", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "lib-libxml": ">=2.6.20", "php": "^7.1 || ^8.0", "sabre/uri": ">=1.0,<3.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.17.1||3.63.2", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6"}, "type": "library", "autoload": {"files": ["lib/Deserializer/functions.php", "lib/Serializer/functions.php"], "psr-4": {"Sabre\\Xml\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "sabre/xml is an XML library that you may not hate.", "homepage": "https://sabre.io/xml/", "keywords": ["XMLReader", "XMLWriter", "dom", "xml"], "support": {"forum": "https://groups.google.com/group/sabredav-discuss", "issues": "https://github.com/sabre-io/xml/issues", "source": "https://github.com/fruux/sabre-xml"}, "time": "2024-09-06T07:37:46+00:00"}, {"name": "smalot/pdfparser", "version": "v2.12.0", "source": {"type": "git", "url": "https://github.com/smalot/pdfparser.git", "reference": "8440edbf58c8596074e78ada38dcb0bd041a5948"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/smalot/pdfparser/zipball/8440edbf58c8596074e78ada38dcb0bd041a5948", "reference": "8440edbf58c8596074e78ada38dcb0bd041a5948", "shasum": ""}, "require": {"ext-iconv": "*", "ext-zlib": "*", "php": ">=7.1", "symfony/polyfill-mbstring": "^1.18"}, "type": "library", "autoload": {"psr-0": {"Smalot\\PdfParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Pdf parser library. Can read and extract information from pdf file.", "homepage": "https://www.pdfparser.org", "keywords": ["extract", "parse", "parser", "pdf", "text"], "support": {"issues": "https://github.com/smalot/pdfparser/issues", "source": "https://github.com/smalot/pdfparser/tree/v2.12.0"}, "time": "2025-03-31T13:16:09+00:00"}, {"name": "steven<PERSON><PERSON><PERSON>/oauth2-keycloak", "version": "5.1.0", "source": {"type": "git", "url": "https://github.com/stevenmaguire/oauth2-keycloak.git", "reference": "1b690b7377dfe7a23e1590373f37e12cf40a6d75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stevenmaguire/oauth2-keycloak/zipball/1b690b7377dfe7a23e1590373f37e12cf40a6d75", "reference": "1b690b7377dfe7a23e1590373f37e12cf40a6d75", "shasum": ""}, "require": {"firebase/php-jwt": "^6.0", "league/oauth2-client": "^2.0", "php": "~7.2 || ~8.0"}, "require-dev": {"mockery/mockery": "~1.5.0", "phpunit/phpunit": "~9.6.4", "squizlabs/php_codesniffer": "~3.7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Stevenmaguire\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "stevenmagu<PERSON>@gmail.com", "homepage": "https://github.com/stevenmaguire"}], "description": "Keycloak OAuth 2.0 Client Provider for The PHP League OAuth2-Client", "keywords": ["authorisation", "authorization", "client", "keycloak", "o<PERSON>h", "oauth2"], "support": {"issues": "https://github.com/stevenmaguire/oauth2-keycloak/issues", "source": "https://github.com/stevenmaguire/oauth2-keycloak/tree/5.1.0"}, "time": "2023-10-24T06:10:44+00:00"}, {"name": "symfony/asset", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/asset.git", "reference": "cb926cd59fefa1f9b4900b3695f0f846797ba5c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/asset/zipball/cb926cd59fefa1f9b4900b3695f0f846797ba5c0", "reference": "cb926cd59fefa1f9b4900b3695f0f846797ba5c0", "shasum": ""}, "require": {"php": ">=8.2"}, "conflict": {"symfony/http-foundation": "<6.4"}, "require-dev": {"symfony/http-client": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Asset\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Manages URL generation and versioning of web assets such as CSS stylesheets, JavaScript files and image files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/asset/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-25T15:15:23+00:00"}, {"name": "symfony/browser-kit", "version": "v7.2.4", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "8ce0ee23857d87d5be493abba2d52d1f9e49da61"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/8ce0ee23857d87d5be493abba2d52d1f9e49da61", "reference": "8ce0ee23857d87d5be493abba2d52d1f9e49da61", "shasum": ""}, "require": {"php": ">=8.2", "symfony/dom-crawler": "^6.4|^7.0"}, "require-dev": {"symfony/css-selector": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Simulates the behavior of a web browser, allowing you to make requests, click on links and submit forms programmatically", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/browser-kit/tree/v7.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-14T14:27:24+00:00"}, {"name": "symfony/cache", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "9131e3018872d2ebb6fe8a9a4d6631273513d42c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/9131e3018872d2ebb6fe8a9a4d6631273513d42c", "reference": "9131e3018872d2ebb6fe8a9a4d6631273513d42c", "shasum": ""}, "require": {"php": ">=8.2", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^2.5|^3", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.4|^7.0"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/dependency-injection": "<6.4", "symfony/http-kernel": "<6.4", "symfony/var-dumper": "<6.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/clock": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/filesystem": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-25T15:54:33+00:00"}, {"name": "symfony/cache-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "15a4f8e5cd3bce9aeafc882b1acab39ec8de2c1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/15a4f8e5cd3bce9aeafc882b1acab39ec8de2c1b", "reference": "15a4f8e5cd3bce9aeafc882b1acab39ec8de2c1b", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/clock", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/clock.git", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/clock/zipball/b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "shasum": ""}, "require": {"php": ">=8.2", "psr/clock": "^1.0", "symfony/polyfill-php83": "^1.28"}, "provide": {"psr/clock-implementation": "1.0"}, "type": "library", "autoload": {"files": ["Resources/now.php"], "psr-4": {"Symfony\\Component\\Clock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Decouples applications from the system clock", "homepage": "https://symfony.com", "keywords": ["clock", "psr20", "time"], "support": {"source": "https://github.com/symfony/clock/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/config", "version": "v7.2.3", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "7716594aaae91d9141be080240172a92ecca4d44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/7716594aaae91d9141be080240172a92ecca4d44", "reference": "7716594aaae91d9141be080240172a92ecca4d44", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/filesystem": "^7.1", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<6.4", "symfony/service-contracts": "<2.5"}, "require-dev": {"symfony/event-dispatcher": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v7.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-22T12:07:01+00:00"}, {"name": "symfony/console", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "e51498ea18570c062e7df29d05a7003585b19b88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/e51498ea18570c062e7df29d05a7003585b19b88", "reference": "e51498ea18570c062e7df29d05a7003585b19b88", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^6.4|^7.0"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-12T08:11:12+00:00"}, {"name": "symfony/dependency-injection", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "58ab71379f14a741755717cece2868bf41ed45d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/58ab71379f14a741755717cece2868bf41ed45d8", "reference": "58ab71379f14a741755717cece2868bf41ed45d8", "shasum": ""}, "require": {"php": ">=8.2", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^3.5", "symfony/var-exporter": "^6.4.20|^7.2.5"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<6.4", "symfony/finder": "<6.4", "symfony/yaml": "<6.4"}, "provide": {"psr/container-implementation": "1.1|2.0", "symfony/service-implementation": "1.1|2.0|3.0"}, "require-dev": {"symfony/config": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-13T12:21:46+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/doctrine-bridge", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-bridge.git", "reference": "f8a298bbb8eaca08d787bf4d4c74728f1cf98922"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-bridge/zipball/f8a298bbb8eaca08d787bf4d4c74728f1cf98922", "reference": "f8a298bbb8eaca08d787bf4d4c74728f1cf98922", "shasum": ""}, "require": {"doctrine/event-manager": "^2", "doctrine/persistence": "^3.1|^4", "php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"doctrine/collections": "<1.8", "doctrine/dbal": "<3.6", "doctrine/lexer": "<1.1", "doctrine/orm": "<2.15", "symfony/cache": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/form": "<6.4.6|>=7,<7.0.6", "symfony/http-foundation": "<6.4", "symfony/http-kernel": "<6.4", "symfony/lock": "<6.4", "symfony/messenger": "<6.4", "symfony/property-info": "<6.4", "symfony/security-bundle": "<6.4", "symfony/security-core": "<6.4", "symfony/validator": "<6.4"}, "require-dev": {"doctrine/collections": "^1.8|^2.0", "doctrine/data-fixtures": "^1.1|^2", "doctrine/dbal": "^3.6|^4", "doctrine/orm": "^2.15|^3", "psr/log": "^1|^2|^3", "symfony/cache": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/doctrine-messenger": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/form": "^6.4.6|^7.0.6", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/security-core": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/type-info": "^7.1", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Doctrine with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/doctrine-bridge/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-25T15:54:33+00:00"}, {"name": "symfony/dom-crawler", "version": "v7.2.4", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "19cc7b08efe9ad1ab1b56e0948e8d02e15ed3ef7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/19cc7b08efe9ad1ab1b56e0948e8d02e15ed3ef7", "reference": "19cc7b08efe9ad1ab1b56e0948e8d02e15ed3ef7", "shasum": ""}, "require": {"masterminds/html5": "^2.6", "php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/css-selector": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases DOM navigation for HTML and XML documents", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v7.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-17T15:53:07+00:00"}, {"name": "symfony/error-handler", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "102be5e6a8e4f4f3eb3149bcbfa33a80d1ee374b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/102be5e6a8e4f4f3eb3149bcbfa33a80d1ee374b", "reference": "102be5e6a8e4f4f3eb3149bcbfa33a80d1ee374b", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^6.4|^7.0"}, "conflict": {"symfony/deprecation-contracts": "<2.5", "symfony/http-kernel": "<6.4"}, "require-dev": {"symfony/deprecation-contracts": "^2.5|^3", "symfony/http-kernel": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-03T07:12:39+00:00"}, {"name": "symfony/event-dispatcher", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/910c5db85a5356d0fea57680defec4e99eb9c8c1", "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1", "shasum": ""}, "require": {"php": ">=8.2", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7642f5e970b672283b7823222ae8ef8bbc160b9f", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/expression-language", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/expression-language.git", "reference": "26f4884a455e755e630a5fc372df124a3578da2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/26f4884a455e755e630a5fc372df124a3578da2e", "reference": "26f4884a455e755e630a5fc372df124a3578da2e", "shasum": ""}, "require": {"php": ">=8.2", "symfony/cache": "^6.4|^7.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an engine that can compile and evaluate expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/expression-language/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-15T11:52:45+00:00"}, {"name": "symfony/filesystem", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/b8dce482de9d7c9fe2891155035a7248ab5c7fdb", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-25T15:15:23+00:00"}, {"name": "symfony/finder", "version": "v7.2.2", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "87a71856f2f56e4100373e92529eed3171695cfb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/87a71856f2f56e4100373e92529eed3171695cfb", "reference": "87a71856f2f56e4100373e92529eed3171695cfb", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.2.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-30T19:00:17+00:00"}, {"name": "symfony/form", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/form.git", "reference": "81c5d4630d58a5ca3cfa7ac6f44070ed02568009"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/form/zipball/81c5d4630d58a5ca3cfa7ac6f44070ed02568009", "reference": "81c5d4630d58a5ca3cfa7ac6f44070ed02568009", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/options-resolver": "^6.4|^7.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-icu": "^1.21", "symfony/polyfill-mbstring": "~1.0", "symfony/property-access": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<6.4", "symfony/error-handler": "<6.4", "symfony/framework-bundle": "<6.4", "symfony/http-kernel": "<6.4", "symfony/translation": "<6.4.3|>=7.0,<7.0.3", "symfony/translation-contracts": "<2.5", "symfony/twig-bridge": "<6.4"}, "require-dev": {"doctrine/collections": "^1.0|^2.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/html-sanitizer": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/security-core": "^6.4|^7.0", "symfony/security-csrf": "^6.4|^7.0", "symfony/translation": "^6.4.3|^7.0.3", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Form\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows to easily create, process and reuse HTML forms", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/form/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-28T12:59:59+00:00"}, {"name": "symfony/framework-bundle", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "c1c6ee8946491b698b067df2258e07918c25da02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/c1c6ee8946491b698b067df2258e07918c25da02", "reference": "c1c6ee8946491b698b067df2258e07918c25da02", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "ext-xml": "*", "php": ">=8.2", "symfony/cache": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^7.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/filesystem": "^7.1", "symfony/finder": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^7.2", "symfony/polyfill-mbstring": "~1.0", "symfony/routing": "^6.4|^7.0"}, "conflict": {"doctrine/persistence": "<1.3", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/asset": "<6.4", "symfony/asset-mapper": "<6.4", "symfony/clock": "<6.4", "symfony/console": "<6.4", "symfony/dom-crawler": "<6.4", "symfony/dotenv": "<6.4", "symfony/form": "<6.4", "symfony/http-client": "<6.4", "symfony/lock": "<6.4", "symfony/mailer": "<6.4", "symfony/messenger": "<6.4", "symfony/mime": "<6.4", "symfony/property-access": "<6.4", "symfony/property-info": "<6.4", "symfony/runtime": "<6.4.13|>=7.0,<7.1.6", "symfony/scheduler": "<6.4.4|>=7.0.0,<7.0.4", "symfony/security-core": "<6.4", "symfony/security-csrf": "<7.2", "symfony/serializer": "<7.2.5", "symfony/stopwatch": "<6.4", "symfony/translation": "<6.4", "symfony/twig-bridge": "<6.4", "symfony/twig-bundle": "<6.4", "symfony/validator": "<6.4", "symfony/web-profiler-bundle": "<6.4", "symfony/webhook": "<7.2", "symfony/workflow": "<6.4"}, "require-dev": {"doctrine/persistence": "^1.3|^2|^3", "dragonmantank/cron-expression": "^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "seld/jsonlint": "^1.10", "symfony/asset": "^6.4|^7.0", "symfony/asset-mapper": "^6.4|^7.0", "symfony/browser-kit": "^6.4|^7.0", "symfony/clock": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/dom-crawler": "^6.4|^7.0", "symfony/dotenv": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/form": "^6.4|^7.0", "symfony/html-sanitizer": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/mailer": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/notifier": "^6.4|^7.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/scheduler": "^6.4.4|^7.0.4", "symfony/security-bundle": "^6.4|^7.0", "symfony/semaphore": "^6.4|^7.0", "symfony/serializer": "^7.2.5", "symfony/stopwatch": "^6.4|^7.0", "symfony/string": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/twig-bundle": "^6.4|^7.0", "symfony/type-info": "^7.1", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/web-link": "^6.4|^7.0", "symfony/webhook": "^7.2", "symfony/workflow": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0", "twig/twig": "^3.12"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration between Symfony components and the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/framework-bundle/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-24T12:37:32+00:00"}, {"name": "symfony/http-client", "version": "v7.2.4", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "78981a2ffef6437ed92d4d7e2a86a82f256c6dc6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/78981a2ffef6437ed92d4d7e2a86a82f256c6dc6", "reference": "78981a2ffef6437ed92d4d7e2a86a82f256c6dc6", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "~3.4.4|^3.5.2", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"amphp/amp": "<2.5", "php-http/discovery": "<1.15", "symfony/http-foundation": "<6.4"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "3.0"}, "require-dev": {"amphp/http-client": "^4.2.1|^5.0", "amphp/http-tunnel": "^1.0|^2.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/amphp-http-client-meta": "^1.0|^2.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "support": {"source": "https://github.com/symfony/http-client/tree/v7.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-13T10:27:23+00:00"}, {"name": "symfony/http-client-contracts", "version": "v3.5.2", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "ee8d807ab20fcb51267fdace50fbe3494c31e645"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/ee8d807ab20fcb51267fdace50fbe3494c31e645", "reference": "ee8d807ab20fcb51267fdace50fbe3494c31e645", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-07T08:49:48+00:00"}, {"name": "symfony/http-foundation", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "371272aeb6286f8135e028ca535f8e4d6f114126"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/371272aeb6286f8135e028ca535f8e4d6f114126", "reference": "371272aeb6286f8135e028ca535f8e4d6f114126", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-25T15:54:33+00:00"}, {"name": "symfony/http-kernel", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "b1fe91bc1fa454a806d3f98db4ba826eb9941a54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/b1fe91bc1fa454a806d3f98db4ba826eb9941a54", "reference": "b1fe91bc1fa454a806d3f98db4ba826eb9941a54", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/browser-kit": "<6.4", "symfony/cache": "<6.4", "symfony/config": "<6.4", "symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<6.4", "symfony/form": "<6.4", "symfony/http-client": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/mailer": "<6.4", "symfony/messenger": "<6.4", "symfony/translation": "<6.4", "symfony/translation-contracts": "<2.5", "symfony/twig-bridge": "<6.4", "symfony/validator": "<6.4", "symfony/var-dumper": "<6.4", "twig/twig": "<3.12"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^6.4|^7.0", "symfony/clock": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/dom-crawler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^7.1", "symfony/routing": "^6.4|^7.0", "symfony/serializer": "^7.1", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0", "symfony/var-exporter": "^6.4|^7.0", "twig/twig": "^3.12"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-28T13:32:50+00:00"}, {"name": "symfony/intl", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/intl.git", "reference": "76bb3462c6c308f8bd97d3c178c2626ae44d4dea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/intl/zipball/76bb3462c6c308f8bd97d3c178c2626ae44d4dea", "reference": "76bb3462c6c308f8bd97d3c178c2626ae44d4dea", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"symfony/string": "<7.1"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Intl\\": ""}, "exclude-from-classmap": ["/Tests/", "/Resources/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides access to the localization data of the ICU library", "homepage": "https://symfony.com", "keywords": ["i18n", "icu", "internationalization", "intl", "l10n", "localization"], "support": {"source": "https://github.com/symfony/intl/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-25T14:26:33+00:00"}, {"name": "symfony/ldap", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/ldap.git", "reference": "48013cfa9d394343162dae7da914112a6206b575"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/ldap/zipball/48013cfa9d394343162dae7da914112a6206b575", "reference": "48013cfa9d394343162dae7da914112a6206b575", "shasum": ""}, "require": {"ext-ldap": "*", "php": ">=8.2", "symfony/options-resolver": "^6.4|^7.0"}, "conflict": {"symfony/options-resolver": "<6.4", "symfony/security-core": "<6.4"}, "require-dev": {"symfony/security-core": "^6.4|^7.0", "symfony/security-http": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Ldap\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a LDAP client for PHP on top of PHP's ldap extension", "homepage": "https://symfony.com", "keywords": ["active-directory", "ldap"], "support": {"source": "https://github.com/symfony/ldap/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-25T15:21:05+00:00"}, {"name": "symfony/mailer", "version": "v7.2.3", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "f3871b182c44997cf039f3b462af4a48fb85f9d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/f3871b182c44997cf039f3b462af4a48fb85f9d3", "reference": "f3871b182c44997cf039f3b462af4a48fb85f9d3", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.2", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/mime": "^7.2", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/messenger": "<6.4", "symfony/mime": "<6.4", "symfony/twig-bridge": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/twig-bridge": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v7.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-27T11:08:17+00:00"}, {"name": "symfony/mime", "version": "v7.2.4", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "87ca22046b78c3feaff04b337f33b38510fd686b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/87ca22046b78c3feaff04b337f33b38510fd686b", "reference": "87ca22046b78c3feaff04b337f33b38510fd686b", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v7.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-19T08:51:20+00:00"}, {"name": "symfony/monolog-bridge", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bridge.git", "reference": "bbae784f0456c5a87c89d7c1a3fcc9cbee976c1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bridge/zipball/bbae784f0456c5a87c89d7c1a3fcc9cbee976c1d", "reference": "bbae784f0456c5a87c89d7c1a3fcc9cbee976c1d", "shasum": ""}, "require": {"monolog/monolog": "^3", "php": ">=8.2", "symfony/http-kernel": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/console": "<6.4", "symfony/http-foundation": "<6.4", "symfony/security-core": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/mailer": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/security-core": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Monolog\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Monolog with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/monolog-bridge/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-14T18:16:08+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.10.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181", "reference": "414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181", "shasum": ""}, "require": {"monolog/monolog": "^1.25.1 || ^2.0 || ^3.0", "php": ">=7.2.5", "symfony/config": "^5.4 || ^6.0 || ^7.0", "symfony/dependency-injection": "^5.4 || ^6.0 || ^7.0", "symfony/http-kernel": "^5.4 || ^6.0 || ^7.0", "symfony/monolog-bridge": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/phpunit-bridge": "^6.3 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "https://symfony.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/symfony/monolog-bundle/issues", "source": "https://github.com/symfony/monolog-bundle/tree/v3.10.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-11-06T17:08:13+00:00"}, {"name": "symfony/options-resolver", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "7da8fbac9dcfef75ffc212235d76b2754ce0cf50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/7da8fbac9dcfef75ffc212235d76b2754ce0cf50", "reference": "7da8fbac9dcfef75ffc212235d76b2754ce0cf50", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-20T11:17:29+00:00"}, {"name": "symfony/password-hasher", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/password-hasher.git", "reference": "d8bd3d66d074c0acba1214a0d42f5941a8e1e94d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/password-hasher/zipball/d8bd3d66d074c0acba1214a0d42f5941a8e1e94d", "reference": "d8bd3d66d074c0acba1214a0d42f5941a8e1e94d", "shasum": ""}, "require": {"php": ">=8.2"}, "conflict": {"symfony/security-core": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/security-core": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PasswordHasher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides password hashing utilities", "homepage": "https://symfony.com", "keywords": ["hashing", "password"], "support": {"source": "https://github.com/symfony/password-hasher/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-icu", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-icu.git", "reference": "d80a05e9904d2c2b9b95929f3e4b5d3a8f418d78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-icu/zipball/d80a05e9904d2c2b9b95929f3e4b5d3a8f418d78", "reference": "d80a05e9904d2c2b9b95929f3e4b5d3a8f418d78", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance and support of other locales than \"en\""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Icu\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's ICU-related data and classes", "homepage": "https://symfony.com", "keywords": ["compatibility", "icu", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-icu/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c36586dcf89a12315939e00ec9b4474adcb1d773", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php84", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php84.git", "reference": "e5493eb51311ab0b1cc2243416613f06ed8f18bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php84/zipball/e5493eb51311ab0b1cc2243416613f06ed8f18bd", "reference": "e5493eb51311ab0b1cc2243416613f06ed8f18bd", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php84\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.4+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php84/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T12:04:04+00:00"}, {"name": "symfony/process", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "87b7c93e57df9d8e39a093d32587702380ff045d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/87b7c93e57df9d8e39a093d32587702380ff045d", "reference": "87b7c93e57df9d8e39a093d32587702380ff045d", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-13T12:21:46+00:00"}, {"name": "symfony/property-access", "version": "v7.2.3", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "b28732e315d81fbec787f838034de7d6c9b2b902"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/b28732e315d81fbec787f838034de7d6c9b2b902", "reference": "b28732e315d81fbec787f838034de7d6c9b2b902", "shasum": ""}, "require": {"php": ">=8.2", "symfony/property-info": "^6.4|^7.0"}, "require-dev": {"symfony/cache": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property-path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v7.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-17T10:56:55+00:00"}, {"name": "symfony/property-info", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "f00fd9685ecdbabe82ca25c7b739ce7bba99302c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/f00fd9685ecdbabe82ca25c7b739ce7bba99302c", "reference": "f00fd9685ecdbabe82ca25c7b739ce7bba99302c", "shasum": ""}, "require": {"php": ">=8.2", "symfony/string": "^6.4|^7.0", "symfony/type-info": "~7.1.9|^7.2.2"}, "conflict": {"phpdocumentor/reflection-docblock": "<5.2", "phpdocumentor/type-resolver": "<1.5.1", "symfony/cache": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/serializer": "<6.4"}, "require-dev": {"phpdocumentor/reflection-docblock": "^5.2", "phpstan/phpdoc-parser": "^1.0|^2.0", "symfony/cache": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-06T16:27:19+00:00"}, {"name": "symfony/routing", "version": "v7.2.3", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "ee9a67edc6baa33e5fae662f94f91fd262930996"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/ee9a67edc6baa33e5fae662f94f91fd262930996", "reference": "ee9a67edc6baa33e5fae662f94f91fd262930996", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"symfony/config": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/yaml": "<6.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v7.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-17T10:56:55+00:00"}, {"name": "symfony/runtime", "version": "v7.2.3", "source": {"type": "git", "url": "https://github.com/symfony/runtime.git", "reference": "8e8d09bd69b7f6c0260dd3d58f37bd4fbdeab5ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/runtime/zipball/8e8d09bd69b7f6c0260dd3d58f37bd4fbdeab5ad", "reference": "8e8d09bd69b7f6c0260dd3d58f37bd4fbdeab5ad", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": ">=8.2"}, "conflict": {"symfony/dotenv": "<6.4"}, "require-dev": {"composer/composer": "^2.6", "symfony/console": "^6.4|^7.0", "symfony/dotenv": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Component\\Runtime\\Internal\\ComposerPlugin"}, "autoload": {"psr-4": {"Symfony\\Component\\Runtime\\": "", "Symfony\\Runtime\\Symfony\\Component\\": "Internal/"}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Enables decoupling PHP applications from global state", "homepage": "https://symfony.com", "keywords": ["runtime"], "support": {"source": "https://github.com/symfony/runtime/tree/v7.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-29T21:39:47+00:00"}, {"name": "symfony/security-bundle", "version": "v7.2.3", "source": {"type": "git", "url": "https://github.com/symfony/security-bundle.git", "reference": "721de227035c6e4c322fb7dd4839586d58bc0cf5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-bundle/zipball/721de227035c6e4c322fb7dd4839586d58bc0cf5", "reference": "721de227035c6e4c322fb7dd4839586d58bc0cf5", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "ext-xml": "*", "php": ">=8.2", "symfony/clock": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4.11|^7.1.4", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/password-hasher": "^6.4|^7.0", "symfony/security-core": "^7.2", "symfony/security-csrf": "^6.4|^7.0", "symfony/security-http": "^7.2", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/browser-kit": "<6.4", "symfony/console": "<6.4", "symfony/framework-bundle": "<6.4", "symfony/http-client": "<6.4", "symfony/ldap": "<6.4", "symfony/serializer": "<6.4", "symfony/twig-bundle": "<6.4", "symfony/validator": "<6.4"}, "require-dev": {"symfony/asset": "^6.4|^7.0", "symfony/browser-kit": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/dom-crawler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/form": "^6.4|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/ldap": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/twig-bridge": "^6.4|^7.0", "symfony/twig-bundle": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0", "twig/twig": "^3.12", "web-token/jwt-library": "^3.3.2|^4.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\SecurityBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of the Security component into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-bundle/tree/v7.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-07T09:39:55+00:00"}, {"name": "symfony/security-core", "version": "v7.2.3", "source": {"type": "git", "url": "https://github.com/symfony/security-core.git", "reference": "466784ffcd0b5a16e05394335897f790b17d07e4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-core/zipball/466784ffcd0b5a16e05394335897f790b17d07e4", "reference": "466784ffcd0b5a16e05394335897f790b17d07e4", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/event-dispatcher-contracts": "^2.5|^3", "symfony/password-hasher": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/http-foundation": "<6.4", "symfony/ldap": "<6.4", "symfony/translation": "<6.4.3|>=7.0,<7.0.3", "symfony/validator": "<6.4"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "psr/container": "^1.1|^2.0", "psr/log": "^1|^2|^3", "symfony/cache": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/ldap": "^6.4|^7.0", "symfony/string": "^6.4|^7.0", "symfony/translation": "^6.4.3|^7.0.3", "symfony/validator": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Core\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Core Library", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-core/tree/v7.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-27T11:08:17+00:00"}, {"name": "symfony/security-csrf", "version": "v7.2.3", "source": {"type": "git", "url": "https://github.com/symfony/security-csrf.git", "reference": "2b4b0c46c901729e4e90719eacd980381f53e0a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-csrf/zipball/2b4b0c46c901729e4e90719eacd980381f53e0a3", "reference": "2b4b0c46c901729e4e90719eacd980381f53e0a3", "shasum": ""}, "require": {"php": ">=8.2", "symfony/security-core": "^6.4|^7.0"}, "conflict": {"symfony/http-foundation": "<6.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Csrf\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - CSRF Library", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-csrf/tree/v7.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-02T18:42:10+00:00"}, {"name": "symfony/security-http", "version": "v7.2.4", "source": {"type": "git", "url": "https://github.com/symfony/security-http.git", "reference": "8478e95e273f8daa23bf4860dbad2a09d3fb3722"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-http/zipball/8478e95e273f8daa23bf4860dbad2a09d3fb3722", "reference": "8478e95e273f8daa23bf4860dbad2a09d3fb3722", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/polyfill-mbstring": "~1.0", "symfony/property-access": "^6.4|^7.0", "symfony/security-core": "^7.2", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/clock": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/http-client-contracts": "<3.0", "symfony/security-bundle": "<6.4", "symfony/security-csrf": "<6.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/cache": "^6.4|^7.0", "symfony/clock": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/http-client-contracts": "^3.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/security-csrf": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "web-token/jwt-library": "^3.3.2|^4.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Http\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - HTTP Integration", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-http/tree/v7.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-11T16:46:20+00:00"}, {"name": "symfony/serializer", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "d8b75b2c8144c29ac43b235738411f7cca6d584d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/d8b75b2c8144c29ac43b235738411f7cca6d584d", "reference": "d8b75b2c8144c29ac43b235738411f7cca6d584d", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<6.4", "symfony/property-access": "<6.4", "symfony/property-info": "<6.4", "symfony/uid": "<6.4", "symfony/validator": "<6.4", "symfony/yaml": "<6.4"}, "require-dev": {"phpdocumentor/reflection-docblock": "^3.2|^4.0|^5.0", "phpstan/phpdoc-parser": "^1.0|^2.0", "seld/jsonlint": "^1.10", "symfony/cache": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^7.2", "symfony/error-handler": "^6.4|^7.0", "symfony/filesystem": "^6.4|^7.0", "symfony/form": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/type-info": "^7.1", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0", "symfony/var-exporter": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Handles serializing and deserializing data structures, including object graphs, into array structures or other formats like XML and JSON.", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-24T12:37:32+00:00"}, {"name": "symfony/service-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/stopwatch", "version": "v7.2.4", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd", "reference": "5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd", "shasum": ""}, "require": {"php": ">=8.2", "symfony/service-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v7.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-24T10:49:57+00:00"}, {"name": "symfony/string", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "446e0d146f991dde3e73f45f2c97a9faad773c82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/446e0d146f991dde3e73f45f2c97a9faad773c82", "reference": "446e0d146f991dde3e73f45f2c97a9faad773c82", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-13T13:31:26+00:00"}, {"name": "symfony/translation", "version": "v7.2.4", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "283856e6981286cc0d800b53bd5703e8e363f05a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/283856e6981286cc0d800b53bd5703e8e363f05a", "reference": "283856e6981286cc0d800b53bd5703e8e363f05a", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"symfony/config": "<6.4", "symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<6.4", "symfony/yaml": "<6.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^4.18|^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v7.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-13T10:27:23+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/4667ff3bd513750603a09c8dedbea942487fb07c", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/twig-bridge", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "b1942d5515b7f0a18e16fd668a04ea952db2b0f2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/b1942d5515b7f0a18e16fd668a04ea952db2b0f2", "reference": "b1942d5515b7f0a18e16fd668a04ea952db2b0f2", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/translation-contracts": "^2.5|^3", "twig/twig": "^3.12"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/console": "<6.4", "symfony/form": "<6.4", "symfony/http-foundation": "<6.4", "symfony/http-kernel": "<6.4", "symfony/mime": "<6.4", "symfony/serializer": "<6.4", "symfony/translation": "<6.4", "symfony/workflow": "<6.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/asset": "^6.4|^7.0", "symfony/asset-mapper": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/emoji": "^7.1", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/form": "^6.4.20|^7.2.5", "symfony/html-sanitizer": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/property-info": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/security-acl": "^2.8|^3.0", "symfony/security-core": "^6.4|^7.0", "symfony/security-csrf": "^6.4|^7.0", "symfony/security-http": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/web-link": "^6.4|^7.0", "symfony/workflow": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0", "twig/cssinliner-extra": "^2.12|^3", "twig/inky-extra": "^2.12|^3", "twig/markdown-extra": "^2.12|^3"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Twig with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bridge/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-28T13:15:09+00:00"}, {"name": "symfony/twig-bundle", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/twig-bundle.git", "reference": "cd2be4563afaef5285bb6e0a06c5445e644a5c01"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bundle/zipball/cd2be4563afaef5285bb6e0a06c5445e644a5c01", "reference": "cd2be4563afaef5285bb6e0a06c5445e644a5c01", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "php": ">=8.2", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/twig-bridge": "^6.4|^7.0", "twig/twig": "^3.12"}, "conflict": {"symfony/framework-bundle": "<6.4", "symfony/translation": "<6.4"}, "require-dev": {"symfony/asset": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/form": "^6.4|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/web-link": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\TwigBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of Twig into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bundle/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-23T08:11:15+00:00"}, {"name": "symfony/type-info", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/type-info.git", "reference": "c4824a6b658294c828e609d3d8dbb4e87f6a375d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/type-info/zipball/c4824a6b658294c828e609d3d8dbb4e87f6a375d", "reference": "c4824a6b658294c828e609d3d8dbb4e87f6a375d", "shasum": ""}, "require": {"php": ">=8.2", "psr/container": "^1.1|^2.0"}, "require-dev": {"phpstan/phpdoc-parser": "^1.0|^2.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\TypeInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Baptiste LEDUC", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts PHP types information.", "homepage": "https://symfony.com", "keywords": ["PHPStan", "phpdoc", "symfony", "type"], "support": {"source": "https://github.com/symfony/type-info/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-24T09:03:36+00:00"}, {"name": "symfony/validator", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "d7edd7f44defbc4e0230512f929b5f4c067bb93e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/d7edd7f44defbc4e0230512f929b5f4c067bb93e", "reference": "d7edd7f44defbc4e0230512f929b5f4c067bb93e", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php83": "^1.27", "symfony/translation-contracts": "^2.5|^3"}, "conflict": {"doctrine/lexer": "<1.1", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<7.0", "symfony/expression-language": "<6.4", "symfony/http-kernel": "<6.4", "symfony/intl": "<6.4", "symfony/property-info": "<6.4", "symfony/translation": "<6.4.3|>=7.0,<7.0.3", "symfony/yaml": "<6.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3|^4", "symfony/cache": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/translation": "^6.4.3|^7.0.3", "symfony/type-info": "^7.1", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/", "/Resources/bin/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-21T15:05:21+00:00"}, {"name": "symfony/var-dumper", "version": "v7.2.3", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "82b478c69745d8878eb60f9a049a4d584996f73a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/82b478c69745d8878eb60f9a049a4d584996f73a", "reference": "82b478c69745d8878eb60f9a049a4d584996f73a", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/uid": "^6.4|^7.0", "twig/twig": "^3.12"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v7.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-17T11:39:41+00:00"}, {"name": "symfony/var-exporter", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "c37b301818bd7288715d40de634f05781b686ace"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/c37b301818bd7288715d40de634f05781b686ace", "reference": "c37b301818bd7288715d40de634f05781b686ace", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-13T12:21:46+00:00"}, {"name": "symfony/web-profiler-bundle", "version": "v7.2.4", "source": {"type": "git", "url": "https://github.com/symfony/web-profiler-bundle.git", "reference": "4ffde1c860a100533b02697d9aaf5f45759ec26a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-profiler-bundle/zipball/4ffde1c860a100533b02697d9aaf5f45759ec26a", "reference": "4ffde1c860a100533b02697d9aaf5f45759ec26a", "shasum": ""}, "require": {"php": ">=8.2", "symfony/config": "^6.4|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/twig-bundle": "^6.4|^7.0", "twig/twig": "^3.12"}, "conflict": {"symfony/form": "<6.4", "symfony/mailer": "<6.4", "symfony/messenger": "<6.4", "symfony/serializer": "<7.2"}, "require-dev": {"symfony/browser-kit": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\WebProfilerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a development tool that gives detailed information about the execution of any request", "homepage": "https://symfony.com", "keywords": ["dev"], "support": {"source": "https://github.com/symfony/web-profiler-bundle/tree/v7.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-14T14:27:24+00:00"}, {"name": "symfony/webpack-encore-bundle", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/symfony/webpack-encore-bundle.git", "reference": "e335394b68a775a9b2bd173a8ba4fd2001f3870c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/webpack-encore-bundle/zipball/e335394b68a775a9b2bd173a8ba4fd2001f3870c", "reference": "e335394b68a775a9b2bd173a8ba4fd2001f3870c", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/asset": "^5.4 || ^6.2 || ^7.0", "symfony/config": "^5.4 || ^6.2 || ^7.0", "symfony/dependency-injection": "^5.4 || ^6.2 || ^7.0", "symfony/http-kernel": "^5.4 || ^6.2 || ^7.0", "symfony/service-contracts": "^1.1.9 || ^2.1.3 || ^3.0"}, "require-dev": {"symfony/framework-bundle": "^5.4 || ^6.2 || ^7.0", "symfony/http-client": "^5.4 || ^6.2 || ^7.0", "symfony/phpunit-bridge": "^5.4 || ^6.2 || ^7.0", "symfony/twig-bundle": "^5.4 || ^6.2 || ^7.0", "symfony/web-link": "^5.4 || ^6.2 || ^7.0"}, "type": "symfony-bundle", "extra": {"thanks": {"url": "https://github.com/symfony/webpack-encore", "name": "symfony/webpack-encore"}}, "autoload": {"psr-4": {"Symfony\\WebpackEncoreBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Integration of your Symfony app with Webpack Encore", "support": {"issues": "https://github.com/symfony/webpack-encore-bundle/issues", "source": "https://github.com/symfony/webpack-encore-bundle/tree/v2.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-02T07:27:19+00:00"}, {"name": "symfony/workflow", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/workflow.git", "reference": "c284a6540f8458be4e7a506f204ee258e8001e85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/workflow/zipball/c284a6540f8458be4e7a506f204ee258e8001e85", "reference": "c284a6540f8458be4e7a506f204ee258e8001e85", "shasum": ""}, "require": {"php": ">=8.2"}, "conflict": {"symfony/event-dispatcher": "<6.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/dependency-injection": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/security-core": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Workflow\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools for managing a workflow or finite state machine", "homepage": "https://symfony.com", "keywords": ["petrinet", "place", "state", "statemachine", "transition", "workflow"], "support": {"source": "https://github.com/symfony/workflow/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/yaml", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "4c4b6f4cfcd7e52053f0c8bfad0f7f30fb924912"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/4c4b6f4cfcd7e52053f0c8bfad0f7f30fb924912", "reference": "4c4b6f4cfcd7e52053f0c8bfad0f7f30fb924912", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-03T07:12:39+00:00"}, {"name": "twig/extra-bundle", "version": "v3.20.0", "source": {"type": "git", "url": "https://github.com/twigphp/twig-extra-bundle.git", "reference": "9df5e1dbb6a68c0665ae5603f6f2c20815647876"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/twig-extra-bundle/zipball/9df5e1dbb6a68c0665ae5603f6f2c20815647876", "reference": "9df5e1dbb6a68c0665ae5603f6f2c20815647876", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/framework-bundle": "^5.4|^6.4|^7.0", "symfony/twig-bundle": "^5.4|^6.4|^7.0", "twig/twig": "^3.2|^4.0"}, "require-dev": {"league/commonmark": "^1.0|^2.0", "symfony/phpunit-bridge": "^6.4|^7.0", "twig/cache-extra": "^3.0", "twig/cssinliner-extra": "^3.0", "twig/html-extra": "^3.0", "twig/inky-extra": "^3.0", "twig/intl-extra": "^3.0", "twig/markdown-extra": "^3.0", "twig/string-extra": "^3.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Twig\\Extra\\TwigExtraBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}], "description": "A Symfony bundle for extra Twig extensions", "homepage": "https://twig.symfony.com", "keywords": ["bundle", "extra", "twig"], "support": {"source": "https://github.com/twigphp/twig-extra-bundle/tree/v3.20.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-02-08T09:47:15+00:00"}, {"name": "twig/intl-extra", "version": "v3.20.0", "source": {"type": "git", "url": "https://github.com/twigphp/intl-extra.git", "reference": "05bc5d46b9df9e62399eae53e7c0b0633298b146"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/intl-extra/zipball/05bc5d46b9df9e62399eae53e7c0b0633298b146", "reference": "05bc5d46b9df9e62399eae53e7c0b0633298b146", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/intl": "^5.4|^6.4|^7.0", "twig/twig": "^3.13|^4.0"}, "require-dev": {"symfony/phpunit-bridge": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Twig\\Extra\\Intl\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}], "description": "A Twig extension for Intl", "homepage": "https://twig.symfony.com", "keywords": ["intl", "twig"], "support": {"source": "https://github.com/twigphp/intl-extra/tree/v3.20.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-01-31T20:45:36+00:00"}, {"name": "twig/twig", "version": "v3.20.0", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "3468920399451a384bef53cf7996965f7cd40183"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/3468920399451a384bef53cf7996965f7cd40183", "reference": "3468920399451a384bef53cf7996965f7cd40183", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"phpstan/phpstan": "^2.0", "psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.4|^7.0"}, "type": "library", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.20.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-02-13T08:34:43+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "packages-dev": [{"name": "myclabs/deep-copy", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "024473a478be9df5fdaca2c793f2232fe788e414"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/024473a478be9df5fdaca2c793f2232fe788e414", "reference": "024473a478be9df5fdaca2c793f2232fe788e414", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2025-02-12T12:17:51+00:00"}, {"name": "nikic/php-parser", "version": "v5.4.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "447a020a1f875a434d62f2a401f53b82a396e494"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/447a020a1f875a434d62f2a401f53b82a396e494", "reference": "447a020a1f875a434d62f2a401f53b82a396e494", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.4.0"}, "time": "2024-12-30T11:07:19+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "****************************************"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/****************************************", "reference": "****************************************", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/4.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T04:45:54+00:00"}, {"name": "sebastian/comparator", "version": "6.3.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "24b8fbc2c8e201bb1308e7b05148d6ab393b6959"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/24b8fbc2c8e201bb1308e7b05148d6ab393b6959", "reference": "24b8fbc2c8e201bb1308e7b05148d6ab393b6959", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "php": ">=8.2", "sebastian/diff": "^6.0", "sebastian/exporter": "^6.0"}, "require-dev": {"phpunit/phpunit": "^11.4"}, "suggest": {"ext-bcmath": "For comparing BcMath\\Number objects"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/6.3.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-03-07T06:57:01+00:00"}, {"name": "sebastian/diff", "version": "6.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "b4ccd857127db5d41a5b676f24b51371d76d8544"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/diff/zipball/b4ccd857127db5d41a5b676f24b51371d76d8544", "reference": "b4ccd857127db5d41a5b676f24b51371d76d8544", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/6.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T04:53:05+00:00"}, {"name": "sebastian/environment", "version": "6.1.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastianbergmann/environment/zipball/8074dbcd93529b357029f5cc5058fd3e43666984", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "https://github.com/sebastian<PERSON>mann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/environment/security/policy", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/6.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-23T08:47:14+00:00"}, {"name": "sebastian/exporter", "version": "6.3.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "3473f61172093b2da7de1fb5782e1f24cc036dc3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/3473f61172093b2da7de1fb5782e1f24cc036dc3", "reference": "3473f61172093b2da7de1fb5782e1f24cc036dc3", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.2", "sebastian/recursion-context": "^6.0"}, "require-dev": {"phpunit/phpunit": "^11.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "security": "https://github.com/sebastian<PERSON>mann/exporter/security/policy", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/6.3.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-12-05T09:17:50+00:00"}, {"name": "sebastian/global-state", "version": "7.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "3be331570a721f9a4b5917f4209773de17f747d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/3be331570a721f9a4b5917f4209773de17f747d7", "reference": "3be331570a721f9a4b5917f4209773de17f747d7", "shasum": ""}, "require": {"php": ">=8.2", "sebastian/object-reflector": "^4.0", "sebastian/recursion-context": "^6.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "https://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/7.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T04:57:36+00:00"}, {"name": "sebastian/object-enumerator", "version": "6.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "f5b498e631a74204185071eb41f33f38d64608aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/f5b498e631a74204185071eb41f33f38d64608aa", "reference": "f5b498e631a74204185071eb41f33f38d64608aa", "shasum": ""}, "require": {"php": ">=8.2", "sebastian/object-reflector": "^4.0", "sebastian/recursion-context": "^6.0"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/6.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T05:00:13+00:00"}, {"name": "sebastian/object-reflector", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "6e1a43b411b2ad34146dee7524cb13a068bb35f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/6e1a43b411b2ad34146dee7524cb13a068bb35f9", "reference": "6e1a43b411b2ad34146dee7524cb13a068bb35f9", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/4.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T05:01:32+00:00"}, {"name": "sebastian/recursion-context", "version": "6.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "694d156164372abbd149a4b85ccda2e4670c0e16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/694d156164372abbd149a4b85ccda2e4670c0e16", "reference": "694d156164372abbd149a4b85ccda2e4670c0e16", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/6.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T05:10:34+00:00"}, {"name": "sebastian/resource-operations", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "05d5692a7993ecccd56a03e40cd7e5b09b1d404e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/05d5692a7993ecccd56a03e40cd7e5b09b1d404e", "reference": "05d5692a7993ecccd56a03e40cd7e5b09b1d404e", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/3.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-14T16:00:52+00:00"}, {"name": "sebastian/version", "version": "5.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c687e3387b99f5b03b6caa64c74b63e2936ff874"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c687e3387b99f5b03b6caa64c74b63e2936ff874", "reference": "c687e3387b99f5b03b6caa64c74b63e2936ff874", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/version/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/5.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-10-09T05:16:32+00:00"}, {"name": "symfony/dotenv", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "28347a897771d0c28e99b75166dd2689099f3045"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/28347a897771d0c28e99b75166dd2689099f3045", "reference": "28347a897771d0c28e99b75166dd2689099f3045", "shasum": ""}, "require": {"php": ">=8.2"}, "conflict": {"symfony/console": "<6.4", "symfony/process": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "support": {"source": "https://github.com/symfony/dotenv/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-27T11:18:42+00:00"}, {"name": "symfony/flex", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/symfony/flex.git", "reference": "8ce1acd9842abe0e9b4c4a0bd3f259859516c018"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/flex/zipball/8ce1acd9842abe0e9b4c4a0bd3f259859516c018", "reference": "8ce1acd9842abe0e9b4c4a0bd3f259859516c018", "shasum": ""}, "require": {"composer-plugin-api": "^2.1", "php": ">=8.0"}, "conflict": {"composer/semver": "<1.7.2"}, "require-dev": {"composer/composer": "^2.1", "symfony/dotenv": "^5.4|^6.0", "symfony/filesystem": "^5.4|^6.0", "symfony/phpunit-bridge": "^5.4|^6.0", "symfony/process": "^5.4|^6.0"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Flex\\Flex"}, "autoload": {"psr-4": {"Symfony\\Flex\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin for Symfony", "support": {"issues": "https://github.com/symfony/flex/issues", "source": "https://github.com/symfony/flex/tree/v2.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-03T07:50:46+00:00"}, {"name": "symfony/maker-bundle", "version": "v1.62.1", "source": {"type": "git", "url": "https://github.com/symfony/maker-bundle.git", "reference": "468ff2708200c95ebc0d85d3174b6c6711b8a590"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/maker-bundle/zipball/468ff2708200c95ebc0d85d3174b6c6711b8a590", "reference": "468ff2708200c95ebc0d85d3174b6c6711b8a590", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "nikic/php-parser": "^4.18|^5.0", "php": ">=8.1", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/deprecation-contracts": "^2.2|^3", "symfony/filesystem": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/process": "^6.4|^7.0"}, "conflict": {"doctrine/doctrine-bundle": "<2.10", "doctrine/orm": "<2.15"}, "require-dev": {"composer/semver": "^3.0", "doctrine/doctrine-bundle": "^2.5.0", "doctrine/orm": "^2.15|^3", "symfony/http-client": "^6.4|^7.0", "symfony/phpunit-bridge": "^6.4.1|^7.0", "symfony/security-core": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0", "twig/twig": "^3.0|^4.x-dev"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MakerBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Maker helps you create empty commands, controllers, form classes, tests and more so you can forget about writing boilerplate code.", "homepage": "https://symfony.com/doc/current/bundles/SymfonyMakerBundle/index.html", "keywords": ["code generator", "dev", "generator", "scaffold", "scaffolding"], "support": {"issues": "https://github.com/symfony/maker-bundle/issues", "source": "https://github.com/symfony/maker-bundle/tree/v1.62.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-15T00:21:40+00:00"}, {"name": "symfony/phpunit-bridge", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/phpunit-bridge.git", "reference": "2bbde92ab25a0e2c88160857af7be9db5da0d145"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/phpunit-bridge/zipball/2bbde92ab25a0e2c88160857af7be9db5da0d145", "reference": "2bbde92ab25a0e2c88160857af7be9db5da0d145", "shasum": ""}, "require": {"php": ">=7.2.5"}, "conflict": {"phpunit/phpunit": "<7.5|9.1.2"}, "require-dev": {"symfony/deprecation-contracts": "^2.5|^3.0", "symfony/error-handler": "^5.4|^6.4|^7.0", "symfony/polyfill-php81": "^1.27"}, "bin": ["bin/simple-phpunit"], "type": "symfony-bridge", "extra": {"thanks": {"url": "https://github.com/sebastian<PERSON>mann/phpunit", "name": "phpunit/phpunit"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Bridge\\PhpUnit\\": ""}, "exclude-from-classmap": ["/Tests/", "/bin/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides utilities for PHPUnit, especially user deprecation notices management", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/phpunit-bridge/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-13T16:15:23+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^8.3.19", "ext-json": "*", "ext-pdo": "*", "ext-simplexml": "*"}, "platform-dev": [], "plugin-api-version": "2.6.0"}