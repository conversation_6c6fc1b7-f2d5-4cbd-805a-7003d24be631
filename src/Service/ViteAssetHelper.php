<?php

namespace App\Service;

class ViteAssetHelper
{
    private array $manifest;
    public string $publicPath;

    public function __construct(
        string $projectDir,
        string $manifestPath = '/public/build/manifest.json',
        string $publicPath = '/build/'
    ) {
        $this->publicPath = $publicPath;

        $fullManifestPath = $projectDir . $manifestPath;

        $this->manifest = file_exists($fullManifestPath)
            ? json_decode(file_get_contents($fullManifestPath), true)
            : [];
    }

    public function asset(string $entry): ?string
    {
        return $this->manifest[$entry]['file'] ?? null;
    }

    public function cssFiles(string $entry): array
    {
        return $this->manifest[$entry]['css'] ?? [];
    }

    public function fullUrl(string $entry): ?string
    {
        $file = $this->asset($entry);
        return $file ? $this->publicPath . $file : null;
    }
}
