<?php

namespace App\Twig;

use App\Service\ViteAssetHelper;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class ViteExtension extends AbstractExtension
{
    public function __construct(private ViteAssetHelper $viteAssetHelper) {}

    public function getFunctions(): array
    {
        return [
            new TwigFunction('vite_script', [$this, 'viteScript'], ['is_safe' => ['html']]),
            new TwigFunction('vite_css', [$this, 'viteCss'], ['is_safe' => ['html']]),
        ];
    }

    public function viteScript(string $entry): string
    {
        $url = $this->viteAssetHelper->fullUrl($entry);
        return $url ? sprintf('<script type="module" src="%s"></script>', $url) : '';
    }

    public function viteCss(string $entry): string
    {
        $tags = '';
        foreach ($this->viteAssetHelper->cssFiles($entry) as $css) {
            $tags .= sprintf('<link rel="stylesheet" href="%s">', $this->viteAssetHelper->publicPath . $css);
        }
        return $tags;
    }
}
