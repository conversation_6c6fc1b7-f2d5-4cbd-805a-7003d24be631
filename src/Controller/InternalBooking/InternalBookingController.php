<?php

/**
 * @package App\Controller\InternalBooking
 * <AUTHOR> <<EMAIL>>
 * @copyright ABUS Kransysteme GmbH
 * @license proprietary
 */

namespace App\Controller\InternalBooking;

use App\Exception\ADUserNotFoundException;
use App\Model\ABUSBookingsTool\AppointmentBooking;
use App\Model\ABUSBookingsTool\AppointmentCategory;
use App\Model\ABUSBookingsTool\AppointmentDate;
use App\Model\ABUSBookingsTool\AppointmentEvent;
use App\Model\ABUSBookingsTool\Helpers\Security;
use App\Model\Api\PortalUser\PortalUser;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;

class InternalBookingController extends AbstractController
{
    use Security;

    private PortalUser $portalUser;
    private AppointmentBooking $booking;
    private AppointmentEvent $event;
    private AppointmentCategory $category;
    private AppointmentDate $date;

    /**
     * @param AppointmentBooking $booking
     * @param AppointmentEvent $event
     * @param AppointmentCategory $category
     * @param AppointmentDate $date
     * @param PortalUser $portalUser
     * @throws ADUserNotFoundException
     */
    public function __construct(AppointmentBooking $booking, AppointmentEvent $event, AppointmentCategory $category, AppointmentDate $date, PortalUser $portalUser)
    {
        $this->booking = $booking;
        $this->event = $event;
        $this->category = $category;
        $this->date = $date;
        $this->portalUser = $portalUser;
        $this->portalUser->fetch();
    }

    /**
     * Alle Kategorie entsprechend den Anmelderechten des Benutzers
     */
    #[Route('/bookingsTool/categories', name: 'abus_bookings_tool_categories')]
    public function categories(): Response
    {
        return new JsonResponse($this->category->all($this->portalUser), 200);
    }

    /**
     * Alle Events entsprechend einer Kategorie
     */
    #[Route('/bookingsTool/events/{category}', name: 'abus_bookings_tool_events')]
    public function events(int $category): Response
    {
        if (!$this->checkIfUserHasSufficientRights($this->portalUser, $this->category->findById($category)->getAdpermissiongroups())) {
            throw new AccessDeniedException("Sie haben nicht genug Rechte.");
        }

        return new JsonResponse($this->event->findByCategory($category), 200);
    }


    /**
     * Alle Dates entsprechend einer Veranstaltung
     *
     * @param int $event
     * @return Response
     */
    #[Route('/bookingsTool/dates/{event}', name: 'abus_bookings_tool_dates')]
    public function dates(int $event): Response
    {
        if (!$this->checkIfUserHasSufficientRights($this->portalUser, $this->category->findById($this->event->findById($event)->getCategoryid())->getAdpermissiongroups())) {
            throw new AccessDeniedException("Sie haben nicht genug Rechte.");
        }

        return new JsonResponse($this->date->findByEvent($event), 200);
    }

    /**
     * Buchungen (vergangene oder nicht), die zu einer Veranstaltung gehören
     */
    #[Route('/bookingsTool/all/bookings', name: 'abus_bookings_tool_bookings')]
    public function bookings(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$this->checkIfUserHasSufficientRights($this->portalUser, $this->category->findById($this->event->findById((int)$data['eventID'])->getCategoryid())->getAdpermissiongroups())) {
            throw new AccessDeniedException("Sie haben nicht genug Rechte.");
        }

        $pastDataShow = $data['pastDataShow'] == 'false';

        $bookingDate = null;

        if (!empty($data['bookingDate'])) {
            $bookingDate = date("Y-m-d", strtotime($data['bookingDate']));
        }

        return new JsonResponse($this->booking->all((int)$data['eventID'], $pastDataShow, $bookingDate), 200);
    }

    /**
     * Stonierung einer Buchung durch sein ID
     *
     * @param Request $request
     * @return Response
     */
    #[Route('/bookingsTool/cancel/booking', name: 'abus_bookings_tool_cancel_booking', methods: ['POST'])]
    public function cancelBooking(Request $request): Response
    {
        $data = json_decode($request->getContent(), true);
        if (!$this->checkIfUserHasSufficientRights($this->portalUser, $this->event->findById((int)$data['eventID'])->getAdpermissiongroups())) {
            throw new AccessDeniedException("Sie haben nicht genug Rechte.");
        }
        return new Response(json_encode($this->booking->cancel((int)$data['bookingID'])), 201);
    }

    /**
     * Löschung einer Buchung durch sein ID
     *
     * @param Request $request
     * @return Response
     */
    #[Route('/bookingsTool/delete/booking', name: 'abus_bookings_tool_delete_booking', methods: ['POST'])]
    public function deleteBooking(Request $request): Response
    {
        $data = json_decode($request->getContent(), true);
        if (!$this->checkIfUserHasSufficientRights($this->portalUser, $this->event->findById((int)$data['eventID'])->getAdpermissiongroups())) {
            throw new AccessDeniedException("Sie haben nicht genug Rechte.");
        }
        return new Response(json_encode($this->booking->destroy((int)$data['bookingID'])), 204);
    }

    /**
     * @param string|null $filter
     * @return Response
     */
    #[Route('/bookingsTool/bookings/export/{filter?}', name: 'abus_bookings_tool_bookings_export', methods: ['GET'])]
    public function dataExport(?string $filter): Response
    {
        $data = json_decode($filter, true);

        $bookingDate = null;

        if (!empty($data['bookingDate'])) {
            $bookingDate = date("Y-m-d", strtotime($data['bookingDate']));
        }

        $bookings = $this->booking->all((int)$data['eventID'], (bool)$data['pastDataShow'], $bookingDate);

        $content = "Termin;Ort;Uhrzeit;E-Mail;Name;Personalnummer;Gebuchte Plätze;Status\n";

        foreach ($bookings as $booking) {
            $content .= $booking['Termin'].';' . $booking['Ort'] . ';' . $booking['Uhrzeit'] . ';' . $booking['E-Mail'] . ';' . $booking['Name'] . ';' . $booking['Personalnummer']. ';' . $booking['Gebuchte Plätze']. ';' . $booking['Status'] ."\n";
        }

        $filename = 'portal_export_bookings_'.date('Y_m_d_His').'.csv';

        $response = new Response();
        $response->setStatusCode(200);
        $response->setCharset('UTF-8');
        $response->headers->set('Content-Type', 'application/vnd.ms-excel; charset=UTF-8');
        $response->headers->set('Content-Disposition', 'attachment; filename='.$filename);
        $response->setContent(utf8_decode($content));

        return $response;
    }

    /**
     * @param string|null $filter
     * @return Response
     */
    #[Route('/bookingsTool/bookings/export/emails/{filter?}', name: 'abus_bookings_tool_bookings_export_emails', methods: ['GET'])]
    public function extractEmails(?string $filter): Response
    {
        $data = json_decode($filter, true);

        $bookingDate = null;

        if (!empty($data['bookingDate'])) {
            $bookingDate = date("Y-m-d", strtotime($data['bookingDate']));
        }

        $bookings = $this->booking->all((int)$data['eventID'], (bool)$data['pastDataShow'], $bookingDate);

        $emails = array_map(function ($item) {
            return $item['E-Mail'];
        },$bookings);

        $content = '';

        foreach (array_unique($emails) as $email) {
            $content .= $email.";";
        }

        return new Response($content, 200);
    }

    #[Route('/bookingsTool/bookings', name: 'abus_bookings_tool_index')]
    public function index(): Response
    {
        return $this->render('BookingsTool/booking.html.twig', [
            'user' => $this->portalUser,
            'scrollRightSidebar' => true,
            'openModule' => 'bookingsTool',
        ]);
    }

}
