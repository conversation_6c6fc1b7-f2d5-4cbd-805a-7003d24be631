<?php

/**
 * @package App\Model\ABUSBookingsTool
 * <AUTHOR> <<EMAIL>>
 * @copyright ABUS Kransysteme GmbH
 * @license proprietary
 */

namespace App\Model\ABUSBookingsTool;

use App\Entity\Pimcore\Appointment\AbusAppointmentCategories;
use App\Model\Api\PortalUser\PortalUserInterface;
use Doctrine\ORM\EntityRepository;

class AppointmentCategory
{
    private EntityRepository $repository;

    /**
     * @param EntityRepository $repository
     */
    public function __construct(EntityRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * @param PortalUserInterface $user
     * @return AbusAppointmentCategories[]
     */
    public function all(PortalUserInterface $user): array
    {
        $categories = $this->repository->findAll();

        $userCategories = [];

        foreach ($categories as $category) {
            foreach ($category->getAdpermissiongroups() as $permission) {
                if ($user->hasGroup($permission)) {
                    $userCategories[] = [
                        'id' => $category->getId(),
                        'niceName' => $category->getNicename(),
                        'name' => $category->getName(),
                        'groups' => $category->getAdpermissiongroups()
                    ];
                    break; // Wenn ein Permission für die Kategorie schon gefunden ist, dann braucht man nicht mehr weiter
                }

            }
        }

        return $userCategories;

    }

    /**
     * @param int $id
     * @return AbusAppointmentCategories
     */
    public function findById(int $id): AbusAppointmentCategories
    {
        return $this->repository->find($id);
    }

}
