<?php

/**
 * @package App\Model\ABUSBookingsTool\Helpers\Security
 * <AUTHOR> <<EMAIL>>
 * @copyright ABUS Kransysteme GmbH
 * @license proprietary
 */

namespace App\Model\ABUSBookingsTool\Helpers;

use App\Model\Api\PortalUser\PortalUserInterface;

Trait Security
{
    public function checkIfUserHasSufficientRights(PortalUserInterface $user, array $groups): bool
    {
        foreach ($groups as $group) {
            if ($user->hasGroup($group)) {
                return true;
            }
        }
        return false;
    }
}
